# Test Automation Framework Architecture

## 🏗️ Architecture Overview

The Test Automation Framework follows a layered architecture pattern designed for maintainability, scalability, and reusability.

```
┌─────────────────────────────────────────────────────────────┐
│                    Test Runner Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   TestRunner    │  │  EngineStartup  │  │   NUnit      │ │
│  │     .cs         │  │      .cs        │  │ Integration  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    Helpers      │  │     Models      │  │  Attributes  │ │
│  │   (Utilities)   │  │ (Data Objects)  │  │   (Custom)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Automation Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  WebDriver      │  │   Extensions    │  │  Page Object │ │
│  │    Helper       │  │    Methods      │  │    Models    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Selenium      │  │  Configuration  │  │   Logging    │ │
│  │   WebDriver     │  │   Management    │  │   System     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Core Components

### 1. Engine Startup (`EngineStartup.cs`)
**Purpose**: Framework initialization and configuration setup
**Responsibilities**:
- Module initialization using `[ModuleInitializer]`
- Work directory resolution and validation
- Environment setup before test discovery
- Error handling during startup

**Key Features**:
- Automatic directory detection from configuration
- Support for relative and absolute paths
- User profile directory expansion (`~`)
- Fail-fast error handling

### 2. Test Runner (`TestRunner.cs`)
**Purpose**: Test discovery, execution, and orchestration
**Responsibilities**:
- Dynamic test case discovery from JSON files
- Test case data generation for NUnit
- Test categorization and filtering
- Integration with NUnit framework

**Key Features**:
- Parameterized test execution
- Suite path filtering via command line
- Dynamic test naming and categorization
- Integration with ConfigHelper for test data

### 3. Configuration Management

#### Config System (`Config.cs`, `ConfigHelper.cs`)
**Purpose**: Centralized configuration management
**Components**:
- `Config.cs`: Static configuration access
- `ConfigHelper.cs`: Configuration parsing and test case loading
- `appsettings.json`: Main configuration file

**Features**:
- Environment-specific settings
- Test suite path management
- Base URL configuration for different environments
- Email settings for notifications

### 4. Helper Classes

#### WebDriverHelper (`WebDriverHelper.cs`)
**Purpose**: Selenium WebDriver abstraction and utilities
**Responsibilities**:
- WebDriver lifecycle management
- Common web automation operations
- Wait strategies and timeouts
- Screenshot capture

#### Logger (`Logger.cs`)
**Purpose**: Centralized logging system
**Features**:
- Multiple log levels (Info, Warn, Error, Debug)
- File and console output
- Thread-safe logging
- Integration with test execution flow

#### ReportGenerator (`ReportGenerator.cs`)
**Purpose**: Test result reporting and documentation
**Features**:
- HTML report generation
- Test result aggregation
- Performance metrics
- Integration with email notifications

#### EmailSender (`EmailSender.cs`)
**Purpose**: Automated email notifications
**Features**:
- SMTP integration
- Test result email notifications
- Attachment support for reports
- Configurable recipient lists

### 5. Data Models

#### Core Models
- **TestCase**: Represents individual test scenarios
- **TestSuite**: Collections of related test cases
- **Module**: Functional area groupings
- **ExecutionResult**: Test execution outcomes
- **AppSettings**: Configuration data structure

#### Supporting Models
- **TestData**: Test input data management
- **Parameters**: Dynamic parameter handling
- **Conditions**: Conditional logic for test execution

### 6. Extensions and Attributes

#### WebDriverExtensions (`WebDriverExtensions.cs`)
**Purpose**: Extend Selenium WebDriver functionality
**Features**:
- Custom wait conditions
- Enhanced element interaction methods
- Utility methods for common operations

#### CustomRetryAttribute (`CustomRetryAttribute.cs`)
**Purpose**: Implement retry logic for flaky tests
**Features**:
- Configurable retry attempts
- Delay between retries
- Conditional retry based on exception types

## 🔄 Data Flow

### Test Execution Flow
1. **Initialization**: `EngineStartup` sets up the environment
2. **Discovery**: `TestRunner` discovers test cases from JSON files
3. **Configuration**: `ConfigHelper` loads test data and settings
4. **Execution**: NUnit executes parameterized tests
5. **Reporting**: Results are logged and reports generated
6. **Notification**: Email notifications sent if configured

### Configuration Flow
1. **appsettings.json** → `Config.Settings`
2. **Test Suite JSONs** → `ConfigHelper.GetAllTestCases()`
3. **Runtime Parameters** → `TestContext.Parameters`
4. **Environment Variables** → Dynamic configuration

## 🎯 Design Patterns

### 1. Page Object Model (POM)
- Encapsulation of page elements and actions
- Separation of test logic from page structure
- Reusable page components

### 2. Data-Driven Testing
- JSON-based test data management
- Parameterized test execution
- Environment-specific data sets

### 3. Factory Pattern
- WebDriver creation and management
- Test data object creation
- Report generator instantiation

### 4. Strategy Pattern
- Different wait strategies
- Multiple report formats
- Various execution modes

## 🔒 Error Handling Strategy

### Exception Hierarchy
- **CustomBlockedException**: Application-specific errors
- **WebDriver Exceptions**: Selenium-related errors
- **Configuration Exceptions**: Setup and config errors

### Retry Mechanisms
- Automatic retry for transient failures
- Configurable retry policies
- Exponential backoff strategies

### Logging Strategy
- Structured logging with context
- Error correlation and tracking
- Performance monitoring

## 🚀 Scalability Considerations

### Parallel Execution
- NUnit parallel test execution support
- Thread-safe logging and reporting
- Isolated test data management

### Modular Design
- Pluggable components
- Easy addition of new test modules
- Configurable execution paths

### Performance Optimization
- Lazy loading of test data
- Efficient WebDriver management
- Optimized wait strategies

## 🔧 Extensibility Points

### Adding New Test Types
1. Create new models in `Models/` directory
2. Extend `ConfigHelper` for data loading
3. Add helper methods in `Helpers/`
4. Update configuration schema

### Custom Reporting
1. Implement new report generators
2. Extend `ReportGenerator` base functionality
3. Add new output formats
4. Configure email templates

### New Automation Technologies
1. Add new WebDriver implementations
2. Create technology-specific helpers
3. Extend configuration options
4. Update dependency management
