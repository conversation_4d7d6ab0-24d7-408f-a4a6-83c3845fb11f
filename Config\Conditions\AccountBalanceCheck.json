{"name": "AccountBalanceCheck", "description": "Condition to check if account balance meets minimum requirements for transaction", "isActive": true, "applicableEnvironments": ["UAT", "Production"], "condition": {"allOf": [{"predicate": {"context": "TestData", "field": "AccountBalance", "operator": "GreaterThanOrEqual", "value": "{{minimumBalance}}"}}, {"predicate": {"context": "TestData", "field": "Account<PERSON><PERSON><PERSON>", "operator": "Equals", "value": "Active"}}, {"not": {"predicate": {"context": "TestData", "field": "AccountType", "operator": "Equals", "value": "Frozen"}}}]}, "defaultParameters": {"minimumBalance": "100.00"}, "createdDate": "2024-01-01T00:00:00Z"}