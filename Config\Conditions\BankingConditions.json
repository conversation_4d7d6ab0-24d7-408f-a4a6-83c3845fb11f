{"name": "BankingConditions", "description": "Common banking domain condition configurations", "isActive": true, "applicableEnvironments": ["UAT", "Production", "Pilot"], "condition": {"allOf": [{"environment": {"environments": ["UAT", "Production", "Pilot"]}}, {"featureFlag": {"featureName": "BankingModule", "expectedValue": true}}]}, "defaultParameters": {"environment": "UAT", "timeout": "30000"}, "createdDate": "2024-01-01T00:00:00Z"}