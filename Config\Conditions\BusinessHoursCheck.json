{"name": "BusinessHoursCheck", "description": "Condition to check if current time is within business hours for banking operations", "isActive": true, "applicableEnvironments": ["UAT", "Production", "Pilot"], "condition": {"anyOf": [{"allOf": [{"predicate": {"context": "Runtime", "field": "CurrentHour", "operator": "GreaterThanOrEqual", "value": "9"}}, {"predicate": {"context": "Runtime", "field": "CurrentHour", "operator": "<PERSON><PERSON><PERSON>", "value": "17"}}, {"predicate": {"context": "Runtime", "field": "IsWeekday", "operator": "Equals", "value": "true"}}]}, {"environment": {"targetEnvironment": "UAT"}}]}, "defaultParameters": {"startHour": "9", "endHour": "17"}, "createdDate": "2024-01-01T00:00:00Z"}