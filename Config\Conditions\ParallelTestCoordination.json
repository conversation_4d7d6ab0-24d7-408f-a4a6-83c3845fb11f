{"name": "ParallelTestCoordination", "description": "Condition for coordinating parallel test execution with shared resources", "isActive": true, "applicableEnvironments": ["UAT", "Production", "Pilot"], "condition": {"holdUntil": {"testCaseName": "{{prerequisiteTestCase}}", "expectedStatus": "Completed", "maxWaitTimeMs": 300000, "pollingIntervalMs": 2000}}, "defaultParameters": {"prerequisiteTestCase": "LoginTestCase", "maxWaitTime": "300000"}, "createdDate": "2024-01-01T00:00:00Z"}