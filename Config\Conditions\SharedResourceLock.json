{"name": "SharedResourceLock", "description": "Condition for managing shared test data resources", "isActive": true, "applicableEnvironments": ["UAT", "Production", "Pilot"], "condition": {"holdUntil": {"resourceName": "{{resourceName}}", "maxWaitTimeMs": 180000, "pollingIntervalMs": 1000}}, "defaultParameters": {"resourceName": "TestUserAccount", "maxWaitTime": "180000"}, "createdDate": "2024-01-01T00:00:00Z"}