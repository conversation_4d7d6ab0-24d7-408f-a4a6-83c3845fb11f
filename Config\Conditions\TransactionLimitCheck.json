{"name": "TransactionLimitCheck", "description": "Condition to validate transaction amount against daily/monthly limits", "isActive": true, "applicableEnvironments": ["UAT", "Production", "Pilot"], "condition": {"allOf": [{"predicate": {"context": "TestData", "field": "TransactionAmount", "operator": "LessThanOrEqual", "value": "{{dailyLimit}}"}}, {"predicate": {"context": "TestData", "field": "MonthlyTransactionTotal", "operator": "LessThanOrEqual", "value": "{{monthlyLimit}}"}}, {"anyOf": [{"predicate": {"context": "TestData", "field": "TransactionType", "operator": "NotEquals", "value": "International"}}, {"featureFlag": {"featureName": "InternationalTransfers", "expectedValue": true}}]}]}, "defaultParameters": {"dailyLimit": "10000.00", "monthlyLimit": "50000.00"}, "createdDate": "2024-01-01T00:00:00Z"}