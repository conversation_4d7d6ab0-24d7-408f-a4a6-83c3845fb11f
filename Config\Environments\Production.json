{"name": "Production", "settings": {"transactionLimits": {"dailyLimit": 10000.0, "monthlyLimit": 50000.0, "internationalLimit": 3000.0}, "businessHours": {"startHour": 9, "endHour": 17, "timezone": "UTC+2"}, "accountSettings": {"minimumBalance": 100.0, "overdraftLimit": 500.0, "maintenanceFee": 10.0}, "securitySettings": {"sessionTimeout": 900, "maxLoginAttempts": 3, "passwordComplexity": "high"}, "testingSettings": {"allowNegativeBalances": false, "skipEmailVerification": false, "enableDebugMode": false, "fastTransactionProcessing": false}}, "featureFlags": [{"name": "NewUIDesign", "defaultValue": false, "description": "New UI design disabled in production"}, {"name": "BiometricLogin", "defaultValue": true, "description": "Biometric login enabled in production"}], "connectionStrings": {"database": "Server=prod-db.ebseg.com;Database=BankingProd;Integrated Security=true;", "redis": "prod-redis.ebseg.com:6379", "messageQueue": "amqp://prod-mq.ebseg.com:5672"}, "apiEndpoints": {"authService": "https://api.ebseg.com/auth/v1", "transactionService": "https://api.ebseg.com/transactions/v1", "accountService": "https://api.ebseg.com/accounts/v1", "notificationService": "https://api.ebseg.com/notifications/v1"}}