{"name": "UAT", "settings": {"transactionLimits": {"dailyLimit": 15000.0, "monthlyLimit": 75000.0, "internationalLimit": 5000.0}, "businessHours": {"startHour": 8, "endHour": 18, "timezone": "UTC+2"}, "accountSettings": {"minimumBalance": 50.0, "overdraftLimit": 1000.0, "maintenanceFee": 5.0}, "securitySettings": {"sessionTimeout": 1800, "maxLoginAttempts": 5, "passwordComplexity": "medium"}, "testingSettings": {"allowNegativeBalances": true, "skipEmailVerification": true, "enableDebugMode": true, "fastTransactionProcessing": true}}, "featureFlags": [{"name": "NewUIDesign", "defaultValue": true, "description": "Enable new UI design for UAT testing"}, {"name": "APIv2", "defaultValue": false, "description": "Enable API version 2 for testing"}], "connectionStrings": {"database": "Server=uat-db.ebseg.com;Database=BankingUAT;Integrated Security=true;", "redis": "uat-redis.ebseg.com:6379", "messageQueue": "amqp://uat-mq.ebseg.com:5672"}, "apiEndpoints": {"authService": "https://uat-auth.ebseg.com/api/v1", "transactionService": "https://uat-transactions.ebseg.com/api/v1", "accountService": "https://uat-accounts.ebseg.com/api/v1", "notificationService": "https://uat-notifications.ebseg.com/api/v1"}}