# Advanced Conditions System - Examples and Usage Guide

## Overview

This document provides examples and usage guidelines for the enhanced conditions system in the Test Automation Framework. The system supports environment-based conditions, feature flags, parallel execution coordination, retry logic, and configuration-driven condition management.

## Condition Types

### 1. Environment-Based Conditions

Control test behavior based on the target environment:

```json
{
  "Environment": {
    "TargetEnvironment": "UAT"
  }
}
```

Or check multiple environments:

```json
{
  "Environment": {
    "Environments": ["UAT", "Pilot", "Development"]
  }
}
```

### 2. Feature Flag Conditions

Enable/disable test steps based on feature flags:

```json
{
  "FeatureFlag": {
    "FeatureName": "InternationalTransfers",
    "ExpectedValue": true,
    "EnvironmentOverrides": {
      "Production": false,
      "UAT": true
    }
  }
}
```

### 3. Parallel Execution Conditions

#### Hold Until Test Completion
Wait for another test case to complete:

```json
{
  "HoldUntil": {
    "TestCaseName": "LoginTestCase",
    "ExpectedStatus": "Completed",
    "MaxWaitTimeMs": 300000,
    "PollingIntervalMs": 2000
  }
}
```

#### Hold Until Resource Available
Wait for shared resource to become available:

```json
{
  "HoldUntil": {
    "ResourceName": "TestUserAccount",
    "MaxWaitTimeMs": 180000,
    "PollingIntervalMs": 1000
  }
}
```

### 4. Retry Conditions

Handle flaky UI interactions:

```json
{
  "Retry": {
    "MaxRetries": 3,
    "RetryDelayMs": 500,
    "BackoffMultiplier": 1.2,
    "RetryOnExceptions": [
      "StaleElementReferenceException",
      "ElementNotInteractableException"
    ]
  }
}
```

Network timeout handling:

```json
{
  "Retry": {
    "MaxRetries": 5,
    "RetryDelayMs": 1000,
    "BackoffMultiplier": 1.5,
    "RetryOnExceptions": [
      "WebDriverTimeoutException",
      "TimeoutException",
      "HttpRequestException"
    ]
  }
}
```

### 5. Skip Conditions

Conditionally skip test steps:

```json
{
  "Skip": {
    "Reason": "OTP verification skipped in UAT environment",
    "When": {
      "Environment": {
        "TargetEnvironment": "UAT"
      }
    }
  }
}
```

## Real-World Banking Examples

### Account Balance Validation

```json
{
  "AllOf": [
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "AccountBalance",
        "Operator": "GreaterThanOrEqual",
        "Value": "1000.00"
      }
    },
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "AccountStatus",
        "Operator": "Equals",
        "Value": "Active"
      }
    },
    {
      "Not": {
        "Predicate": {
          "Context": "TestData",
          "Field": "AccountType",
          "Operator": "Equals",
          "Value": "Frozen"
        }
      }
    }
  ]
}
```

### Transaction Limit Validation

```json
{
  "AllOf": [
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "TransactionAmount",
        "Operator": "LessThanOrEqual",
        "Value": "10000.00"
      }
    },
    {
      "AnyOf": [
        {
          "Predicate": {
            "Context": "TestData",
            "Field": "TransactionType",
            "Operator": "NotEquals",
            "Value": "International"
          }
        },
        {
          "FeatureFlag": {
            "FeatureName": "InternationalTransfers",
            "ExpectedValue": true
          }
        }
      ]
    }
  ]
}
```

### Business Hours Check

```json
{
  "AnyOf": [
    {
      "AllOf": [
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "CurrentHour",
            "Operator": "GreaterThanOrEqual",
            "Value": "9"
          }
        },
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "CurrentHour",
            "Operator": "LessThan",
            "Value": "17"
          }
        },
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "IsWeekday",
            "Operator": "Equals",
            "Value": "true"
          }
        }
      ]
    },
    {
      "Environment": {
        "TargetEnvironment": "UAT"
      }
    }
  ]
}
```

## Configuration Management

### Reusable Condition Configurations

Create reusable condition configurations in `Config/Conditions/`:

```json
{
  "name": "AccountBalanceCheck",
  "description": "Condition to check if account balance meets minimum requirements",
  "isActive": true,
  "applicableEnvironments": ["UAT", "Production"],
  "condition": {
    "AllOf": [
      {
        "Predicate": {
          "Context": "TestData",
          "Field": "AccountBalance",
          "Operator": "GreaterThanOrEqual",
          "Value": "{{minimumBalance}}"
        }
      }
    ]
  },
  "defaultParameters": {
    "minimumBalance": "100.00"
  }
}
```

### Using Configuration References

Reference reusable conditions in test cases:

```json
{
  "Condition": {
    "Reference": {
      "Ref": "AccountBalanceCheck.json",
      "Params": {
        "minimumBalance": "1000.00"
      }
    }
  }
}
```

## Environment-Specific Configuration

### Feature Flags per Environment

`Config/FeatureFlags.UAT.json`:
```json
{
  "InternationalTransfers": true,
  "NewUIDesign": true,
  "BiometricLogin": false
}
```

`Config/FeatureFlags.Production.json`:
```json
{
  "InternationalTransfers": true,
  "NewUIDesign": false,
  "BiometricLogin": true
}
```

### Environment Settings

`Config/Environments/UAT.json`:
```json
{
  "name": "UAT",
  "settings": {
    "transactionLimits": {
      "dailyLimit": 15000.00,
      "monthlyLimit": 75000.00
    },
    "testingSettings": {
      "allowNegativeBalances": true,
      "skipEmailVerification": true
    }
  }
}
```

## Best Practices

1. **Use Descriptive Names**: Give conditions meaningful names that describe their purpose
2. **Environment Separation**: Use different configurations for different environments
3. **Parameter Substitution**: Use parameters to make conditions reusable
4. **Logical Grouping**: Group related conditions using AllOf/AnyOf
5. **Error Handling**: Always include appropriate retry conditions for flaky operations
6. **Resource Management**: Use resource locking for shared test data
7. **Feature Flags**: Use feature flags to control test execution based on available features

## Integration with WebDriverHelper

The enhanced condition system is fully integrated into WebDriverHelper with automatic context management and condition evaluation.

### How WebDriverHelper Uses Conditions

1. **Automatic Context Building**: Uses `ContextManager.BuildContext()` to create comprehensive execution context
2. **Enhanced Condition Evaluation**: Uses `ConditionEvaluator.EvaluateWithResult()` for detailed condition results
3. **Parallel Coordination**: Automatically manages test state and resource locking
4. **Retry Logic**: Built-in retry handling for flaky operations
5. **Skip Logic**: Automatic step skipping based on conditions

### WebDriverHelper Integration Features

#### 1. Test State Management
```csharp
// Automatically tracks test execution state
ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), "Running", stepIndex);
```

#### 2. Resource Locking Commands
```json
{
  "Name": "Lock shared test account",
  "Command": "lockResource",
  "Target": "TestAccount_001",
  "Value": "300000"
}
```

#### 3. Resource Release Commands
```json
{
  "Name": "Release shared test account",
  "Command": "releaseResource",
  "Target": "TestAccount_001",
  "Value": "released"
}
```

#### 4. Automatic Retry Execution
When a step has a retry condition, WebDriverHelper automatically uses `RetryConditionEvaluator.ExecuteStepWithRetry()`:

```json
{
  "Name": "Click submit button",
  "Command": "click",
  "Target": "{{Selectors.SubmitButton}}",
  "Condition": {
    "Retry": {
      "MaxRetries": 3,
      "RetryDelayMs": 500,
      "BackoffMultiplier": 1.2,
      "RetryOnExceptions": ["StaleElementReferenceException"]
    }
  }
}
```

#### 5. Comprehensive Context Available
The context includes:
- **Environment**: Current environment, base URLs, environment-specific config
- **TestCase**: Test case details, status, execution time
- **Step**: Current step information
- **Runtime**: Browser info, execution time, thread ID
- **FeatureFlags**: Environment-specific feature flags
- **ParallelExecution**: Running tests, completed tests, locked resources
- **ExternalSystems**: Database connectivity, API status, network latency

### Complete Test Case Example with WebDriverHelper Integration

```json
{
  "TestCaseName": "Advanced Banking Transfer",
  "TestCaseCode": "TC-ADV-001",
  "Environment": "UAT",
  "Steps": [
    {
      "Name": "Wait for login prerequisite",
      "Command": "wait",
      "Target": "condition",
      "Condition": {
        "HoldUntil": {
          "TestCaseName": "LoginTestCase",
          "ExpectedStatus": "Completed",
          "MaxWaitTimeMs": 300000
        }
      }
    },
    {
      "Name": "Lock test account resource",
      "Command": "lockResource",
      "Target": "TestAccount_001",
      "Value": "300000"
    },
    {
      "Name": "Check feature availability",
      "Command": "verify",
      "Target": "feature_enabled",
      "Condition": {
        "FeatureFlag": {
          "FeatureName": "InternationalTransfers",
          "ExpectedValue": true
        }
      }
    },
    {
      "Name": "Enter transfer amount with retry",
      "Command": "input",
      "Target": "{{Selectors.AmountField}}",
      "Value": "{{TestData.Amount}}",
      "Condition": {
        "Retry": {
          "MaxRetries": 3,
          "RetryDelayMs": 500,
          "RetryOnExceptions": ["StaleElementReferenceException", "ElementNotInteractableException"]
        }
      }
    },
    {
      "Name": "Skip OTP in UAT",
      "Command": "input",
      "Target": "{{Selectors.OTPField}}",
      "Value": "123456",
      "Condition": {
        "Skip": {
          "Reason": "OTP skipped in UAT environment",
          "When": {
            "Environment": {
              "TargetEnvironment": "UAT"
            }
          }
        }
      }
    },
    {
      "Name": "Release test account resource",
      "Command": "releaseResource",
      "Target": "TestAccount_001"
    }
  ]
}
```

### WebDriverHelper Condition Processing Flow

1. **Step Preprocessing**: Replace captured data placeholders
2. **Condition Evaluation**: Use `EvaluateStepCondition()` to check all condition types
3. **Action Decision**: Determine if step should stop, skip, or continue
4. **Enhanced Execution**: Use `ExecuteStepWithConditions()` for retry logic and resource management
5. **State Updates**: Update test execution state and parallel coordination
6. **Result Handling**: Process condition results and update test status

### Automatic Features in WebDriverHelper

- **Context Management**: Automatically builds comprehensive execution context
- **State Tracking**: Tracks test execution progress for parallel coordination
- **Resource Management**: Handles resource locking/unlocking automatically
- **Retry Logic**: Executes steps with retry conditions automatically
- **Skip Logic**: Skips steps based on skip conditions
- **Error Handling**: Enhanced error handling with condition-aware messaging
- **Cleanup**: Automatic cleanup of resources and test states

This integration provides seamless, powerful condition handling while maintaining backward compatibility with existing test cases.
