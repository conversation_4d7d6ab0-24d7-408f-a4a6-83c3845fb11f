# Advanced Conditions System - Examples and Usage Guide

## Overview

This document provides examples and usage guidelines for the enhanced conditions system in the Test Automation Framework. The system supports environment-based conditions, feature flags, parallel execution coordination, retry logic, and configuration-driven condition management.

## Condition Types

### 1. Environment-Based Conditions

Control test behavior based on the target environment:

```json
{
  "Environment": {
    "TargetEnvironment": "UAT"
  }
}
```

Or check multiple environments:

```json
{
  "Environment": {
    "Environments": ["UAT", "Pilot", "Development"]
  }
}
```

### 2. Feature Flag Conditions

Enable/disable test steps based on feature flags:

```json
{
  "FeatureFlag": {
    "FeatureName": "InternationalTransfers",
    "ExpectedValue": true,
    "EnvironmentOverrides": {
      "Production": false,
      "UAT": true
    }
  }
}
```

### 3. Parallel Execution Conditions

#### Hold Until Test Completion
Wait for another test case to complete:

```json
{
  "HoldUntil": {
    "TestCaseName": "LoginTestCase",
    "ExpectedStatus": "Completed",
    "MaxWaitTimeMs": 300000,
    "PollingIntervalMs": 2000
  }
}
```

#### Hold Until Resource Available
Wait for shared resource to become available:

```json
{
  "HoldUntil": {
    "ResourceName": "TestUserAccount",
    "MaxWaitTimeMs": 180000,
    "PollingIntervalMs": 1000
  }
}
```

### 4. Retry Conditions

Handle flaky UI interactions:

```json
{
  "Retry": {
    "MaxRetries": 3,
    "RetryDelayMs": 500,
    "BackoffMultiplier": 1.2,
    "RetryOnExceptions": [
      "StaleElementReferenceException",
      "ElementNotInteractableException"
    ]
  }
}
```

Network timeout handling:

```json
{
  "Retry": {
    "MaxRetries": 5,
    "RetryDelayMs": 1000,
    "BackoffMultiplier": 1.5,
    "RetryOnExceptions": [
      "WebDriverTimeoutException",
      "TimeoutException",
      "HttpRequestException"
    ]
  }
}
```

### 5. Skip Conditions

Conditionally skip test steps:

```json
{
  "Skip": {
    "Reason": "OTP verification skipped in UAT environment",
    "When": {
      "Environment": {
        "TargetEnvironment": "UAT"
      }
    }
  }
}
```

## Real-World Banking Examples

### Account Balance Validation

```json
{
  "AllOf": [
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "AccountBalance",
        "Operator": "GreaterThanOrEqual",
        "Value": "1000.00"
      }
    },
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "AccountStatus",
        "Operator": "Equals",
        "Value": "Active"
      }
    },
    {
      "Not": {
        "Predicate": {
          "Context": "TestData",
          "Field": "AccountType",
          "Operator": "Equals",
          "Value": "Frozen"
        }
      }
    }
  ]
}
```

### Transaction Limit Validation

```json
{
  "AllOf": [
    {
      "Predicate": {
        "Context": "TestData",
        "Field": "TransactionAmount",
        "Operator": "LessThanOrEqual",
        "Value": "10000.00"
      }
    },
    {
      "AnyOf": [
        {
          "Predicate": {
            "Context": "TestData",
            "Field": "TransactionType",
            "Operator": "NotEquals",
            "Value": "International"
          }
        },
        {
          "FeatureFlag": {
            "FeatureName": "InternationalTransfers",
            "ExpectedValue": true
          }
        }
      ]
    }
  ]
}
```

### Business Hours Check

```json
{
  "AnyOf": [
    {
      "AllOf": [
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "CurrentHour",
            "Operator": "GreaterThanOrEqual",
            "Value": "9"
          }
        },
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "CurrentHour",
            "Operator": "LessThan",
            "Value": "17"
          }
        },
        {
          "Predicate": {
            "Context": "Runtime",
            "Field": "IsWeekday",
            "Operator": "Equals",
            "Value": "true"
          }
        }
      ]
    },
    {
      "Environment": {
        "TargetEnvironment": "UAT"
      }
    }
  ]
}
```

## Configuration Management

### Reusable Condition Configurations

Create reusable condition configurations in `Config/Conditions/`:

```json
{
  "name": "AccountBalanceCheck",
  "description": "Condition to check if account balance meets minimum requirements",
  "isActive": true,
  "applicableEnvironments": ["UAT", "Production"],
  "condition": {
    "AllOf": [
      {
        "Predicate": {
          "Context": "TestData",
          "Field": "AccountBalance",
          "Operator": "GreaterThanOrEqual",
          "Value": "{{minimumBalance}}"
        }
      }
    ]
  },
  "defaultParameters": {
    "minimumBalance": "100.00"
  }
}
```

### Using Configuration References

Reference reusable conditions in test cases:

```json
{
  "Condition": {
    "Reference": {
      "Ref": "AccountBalanceCheck.json",
      "Params": {
        "minimumBalance": "1000.00"
      }
    }
  }
}
```

## Environment-Specific Configuration

### Feature Flags per Environment

`Config/FeatureFlags.UAT.json`:
```json
{
  "InternationalTransfers": true,
  "NewUIDesign": true,
  "BiometricLogin": false
}
```

`Config/FeatureFlags.Production.json`:
```json
{
  "InternationalTransfers": true,
  "NewUIDesign": false,
  "BiometricLogin": true
}
```

### Environment Settings

`Config/Environments/UAT.json`:
```json
{
  "name": "UAT",
  "settings": {
    "transactionLimits": {
      "dailyLimit": 15000.00,
      "monthlyLimit": 75000.00
    },
    "testingSettings": {
      "allowNegativeBalances": true,
      "skipEmailVerification": true
    }
  }
}
```

## Best Practices

1. **Use Descriptive Names**: Give conditions meaningful names that describe their purpose
2. **Environment Separation**: Use different configurations for different environments
3. **Parameter Substitution**: Use parameters to make conditions reusable
4. **Logical Grouping**: Group related conditions using AllOf/AnyOf
5. **Error Handling**: Always include appropriate retry conditions for flaky operations
6. **Resource Management**: Use resource locking for shared test data
7. **Feature Flags**: Use feature flags to control test execution based on available features

## Integration with Test Cases

The enhanced condition system integrates seamlessly with existing test cases. Simply add condition objects to test steps to enable advanced behavior control.

Example integration:
```json
{
  "Name": "Transfer Money",
  "Command": "click",
  "Target": "{{Selectors.TransferButton}}",
  "Condition": {
    "AllOf": [
      {
        "Reference": {
          "Ref": "AccountBalanceCheck.json",
          "Params": {
            "minimumBalance": "1000.00"
          }
        }
      },
      {
        "FeatureFlag": {
          "FeatureName": "InternationalTransfers",
          "ExpectedValue": true
        }
      }
    ]
  }
}
```

This system provides powerful capabilities for managing complex test scenarios while maintaining clean, readable, and maintainable test configurations.
