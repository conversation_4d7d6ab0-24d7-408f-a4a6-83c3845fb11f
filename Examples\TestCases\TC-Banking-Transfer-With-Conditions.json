{"TestCaseName": "Banking Transfer with Advanced Conditions", "TestCaseCode": "TC-BANK-001", "Description": "Demonstrates advanced condition usage for banking transfer scenarios", "Labels": ["Banking", "Transfer", "Conditions", "Example"], "Environment": "UAT", "Steps": [{"Name": "Wait for prerequisite login test to complete", "Command": "wait", "Target": "condition", "Value": "prerequisite_complete", "Condition": {"Reference": {"Ref": "ParallelTestCoordination.json", "Params": {"prerequisiteTestCase": "TC-LOGIN-001"}}}}, {"Name": "Lock shared test account resource", "Command": "lockResource", "Target": "TestAccount_001", "Value": "300000", "Condition": {"Reference": {"Ref": "SharedResourceLock.json", "Params": {"resourceName": "TestAccount_001"}}}}, {"Name": "Check if international transfers are enabled", "Command": "verify", "Target": "feature_flag", "Value": "enabled", "Condition": {"FeatureFlag": {"FeatureName": "InternationalTransfers", "ExpectedValue": true, "EnvironmentOverrides": {"Production": false, "UAT": true}}}}, {"Name": "Validate account balance before transfer", "Command": "verify", "Target": "{{Selectors.AccountBalance}}", "Value": "sufficient", "Condition": {"Reference": {"Ref": "AccountBalanceCheck.json", "Params": {"minimumBalance": "1000.00"}}}}, {"Name": "Check business hours for large transfers", "Command": "verify", "Target": "business_hours", "Value": "active", "Condition": {"AllOf": [{"Predicate": {"Context": "TestData", "Field": "TransferAmount", "Operator": "GreaterThan", "Value": "5000.00"}}, {"Reference": {"Ref": "BusinessHoursCheck.json"}}]}}, {"Name": "Enter transfer amount with retry on UI flakiness", "Command": "input", "Target": "{{Selectors.TransferAmount}}", "Value": "{{TestData.TransferAmount}}", "Condition": {"Retry": {"MaxRetries": 3, "RetryDelayMs": 500, "BackoffMultiplier": 1.2, "RetryOnExceptions": ["StaleElementReferenceException", "ElementNotInteractableException"]}}}, {"Name": "Validate transaction limits", "Command": "verify", "Target": "transaction_limits", "Value": "within_limits", "Condition": {"Reference": {"Ref": "TransactionLimitCheck.json", "Params": {"dailyLimit": "10000.00", "monthlyLimit": "50000.00"}}}}, {"Name": "Click transfer button with network retry", "Command": "click", "Target": "{{Selectors.TransferButton}}", "Condition": {"Retry": {"MaxRetries": 5, "RetryDelayMs": 1000, "BackoffMultiplier": 1.5, "RetryOnExceptions": ["WebDriverTimeoutException", "TimeoutException", "HttpRequestException"]}}}, {"Name": "Skip OTP verification in UAT environment", "Command": "input", "Target": "{{Selectors.OTPField}}", "Value": "123456", "Condition": {"Skip": {"Reason": "OTP verification skipped in UAT environment", "When": {"Environment": {"TargetEnvironment": "UAT"}}}}}, {"Name": "Verify transfer success with environment-specific validation", "Command": "verify", "Target": "{{Selectors.SuccessMessage}}", "Value": "Transfer completed successfully", "Condition": {"AnyOf": [{"AllOf": [{"Environment": {"TargetEnvironment": "Production"}}, {"Predicate": {"Context": "TestData", "Field": "RequireEmailConfirmation", "Operator": "Equals", "Value": "true"}}]}, {"Environment": {"Environments": ["UAT", "Pilot"]}}]}}, {"Name": "Release shared test account resource", "Command": "releaseResource", "Target": "TestAccount_001", "Value": "released"}]}