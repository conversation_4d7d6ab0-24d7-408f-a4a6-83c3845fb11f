# WebDriverHelper Conditions - Quick Reference

## How to Use All Condition Types in WebDriverHelper

### 1. Environment-Based Conditions

**Skip step in Production:**
```json
{
  "Name": "Debug step",
  "Command": "click",
  "Target": "{{Selectors.DebugButton}}",
  "Condition": {
    "Skip": {
      "Reason": "Debug features disabled in Production",
      "When": {
        "Environment": {
          "TargetEnvironment": "Production"
        }
      }
    }
  }
}
```

**Different behavior per environment:**
```json
{
  "Name": "Set transaction limit",
  "Command": "input",
  "Target": "{{Selectors.LimitField}}",
  "Value": "{{TestData.TransactionLimit}}",
  "Condition": {
    "Environment": {
      "Environments": ["UAT", "Development"]
    }
  }
}
```

### 2. Feature Flag Conditions

**Execute only if feature is enabled:**
```json
{
  "Name": "Use biometric login",
  "Command": "click",
  "Target": "{{Selectors.BiometricButton}}",
  "Condition": {
    "FeatureFlag": {
      "FeatureName": "BiometricLogin",
      "ExpectedValue": true,
      "EnvironmentOverrides": {
        "UAT": false,
        "Production": true
      }
    }
  }
}
```

### 3. Parallel Execution Conditions

**Wait for prerequisite test:**
```json
{
  "Name": "Wait for login completion",
  "Command": "wait",
  "Target": "prerequisite",
  "Condition": {
    "HoldUntil": {
      "TestCaseName": "TC-LOGIN-001",
      "ExpectedStatus": "Completed",
      "MaxWaitTimeMs": 300000,
      "PollingIntervalMs": 2000
    }
  }
}
```

**Lock shared resource:**
```json
{
  "Name": "Lock test user account",
  "Command": "lockResource",
  "Target": "TestUser_Premium_001",
  "Value": "300000"
}
```

**Release shared resource:**
```json
{
  "Name": "Release test user account",
  "Command": "releaseResource",
  "Target": "TestUser_Premium_001"
}
```

### 4. Retry Conditions

**Retry flaky UI interactions:**
```json
{
  "Name": "Click submit with retry",
  "Command": "click",
  "Target": "{{Selectors.SubmitButton}}",
  "Condition": {
    "Retry": {
      "MaxRetries": 5,
      "RetryDelayMs": 500,
      "BackoffMultiplier": 1.2,
      "RetryOnExceptions": [
        "StaleElementReferenceException",
        "ElementNotInteractableException",
        "ElementClickInterceptedException"
      ]
    }
  }
}
```

**Retry network operations:**
```json
{
  "Name": "Load page with network retry",
  "Command": "navigate",
  "Target": "{{TestData.PageUrl}}",
  "Condition": {
    "Retry": {
      "MaxRetries": 3,
      "RetryDelayMs": 1000,
      "BackoffMultiplier": 1.5,
      "RetryOnExceptions": [
        "WebDriverTimeoutException",
        "TimeoutException",
        "HttpRequestException"
      ]
    }
  }
}
```

### 5. Complex Condition Combinations

**Multiple conditions with AllOf:**
```json
{
  "Name": "Transfer money",
  "Command": "click",
  "Target": "{{Selectors.TransferButton}}",
  "Condition": {
    "AllOf": [
      {
        "Reference": {
          "Ref": "AccountBalanceCheck.json",
          "Params": {
            "minimumBalance": "1000.00"
          }
        }
      },
      {
        "FeatureFlag": {
          "FeatureName": "InternationalTransfers",
          "ExpectedValue": true
        }
      },
      {
        "Environment": {
          "Environments": ["UAT", "Production"]
        }
      }
    ]
  }
}
```

**Alternative conditions with AnyOf:**
```json
{
  "Name": "Login with multiple methods",
  "Command": "click",
  "Target": "{{Selectors.LoginButton}}",
  "Condition": {
    "AnyOf": [
      {
        "FeatureFlag": {
          "FeatureName": "BiometricLogin",
          "ExpectedValue": true
        }
      },
      {
        "FeatureFlag": {
          "FeatureName": "TwoFactorAuthentication",
          "ExpectedValue": true
        }
      },
      {
        "Environment": {
          "TargetEnvironment": "UAT"
        }
      }
    ]
  }
}
```

### 6. Configuration References

**Use reusable condition configurations:**
```json
{
  "Name": "Validate business hours",
  "Command": "verify",
  "Target": "business_hours",
  "Condition": {
    "Reference": {
      "Ref": "BusinessHoursCheck.json",
      "Params": {
        "startHour": "9",
        "endHour": "17"
      }
    }
  }
}
```

### 7. Stop Conditions (Legacy Support)

**Traditional stop condition:**
```json
{
  "Name": "Stop at token step",
  "Command": "click",
  "Target": "{{Selectors.LoginButton}}",
  "Condition": {
    "TestCaseStatus": "TokenStopped",
    "Reference": {
      "Ref": "TokenStepCondition.json",
      "Params": {
        "environment": "UAT",
        "expectedStep": "LoginStep"
      }
    }
  }
}
```

## WebDriverHelper Automatic Features

### Context Available in Conditions

All conditions have access to:
- **Environment.Current**: Current environment name
- **Environment.IsUAT/IsProduction**: Environment flags
- **TestCase.Id/Name/Status**: Test case information
- **Step.Name/Command/Index**: Current step details
- **Runtime.Browsers/ThreadId**: Runtime information
- **FeatureFlags**: All feature flags for current environment
- **ParallelExecution.RunningTests**: Currently running tests
- **ExternalSystems**: Database/API connectivity status

### Automatic Behaviors

1. **State Tracking**: Test execution state automatically tracked
2. **Resource Management**: Resources automatically locked/released
3. **Retry Execution**: Steps with retry conditions automatically retried
4. **Skip Logic**: Steps automatically skipped based on conditions
5. **Context Building**: Comprehensive context automatically built
6. **Error Handling**: Enhanced error messages with condition context

### Best Practices

1. **Use Descriptive Names**: Make condition purposes clear
2. **Combine Conditions**: Use AllOf/AnyOf for complex logic
3. **Environment Separation**: Different behavior per environment
4. **Resource Locking**: Always lock shared test data
5. **Retry Flaky Operations**: Use retry for UI interactions
6. **Feature Flag Control**: Control test execution with feature flags
7. **Configuration Reuse**: Use reference conditions for common patterns

This integration provides powerful, flexible condition handling while maintaining clean, readable test configurations.
