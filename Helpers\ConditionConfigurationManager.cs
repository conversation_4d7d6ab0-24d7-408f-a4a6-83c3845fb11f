using System.Text.Json;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    /// <summary>
    /// Manages configuration-driven condition definitions
    /// </summary>
    public static class ConditionConfigurationManager
    {
        private static readonly Dictionary<string, ConditionConfiguration> _conditionConfigurations = new();
        private static readonly Dictionary<string, Dictionary<string, ConditionConfiguration>> _environmentConditions = new();
        private static readonly object _lock = new();
        private static bool _initialized = false;

        /// <summary>
        /// Initialize condition configurations from files
        /// </summary>
        public static void Initialize()
        {
            if (_initialized) return;

            lock (_lock)
            {
                if (_initialized) return;

                try
                {
                    LoadGlobalConditions();
                    LoadEnvironmentSpecificConditions();
                    _initialized = true;
                    Logger.Log("Condition configuration manager initialized successfully", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error initializing condition configuration manager: {ex.Message}", LogLevel.Error);
                    throw;
                }
            }
        }

        /// <summary>
        /// Get condition configuration by name
        /// </summary>
        public static ConditionConfiguration? GetConditionConfiguration(string name, string? environment = null)
        {
            Initialize();

            // Try environment-specific first
            if (!string.IsNullOrEmpty(environment) && 
                _environmentConditions.TryGetValue(environment, out var envConditions) &&
                envConditions.TryGetValue(name, out var envCondition))
            {
                return envCondition;
            }

            // Fall back to global conditions
            return _conditionConfigurations.TryGetValue(name, out var globalCondition) ? globalCondition : null;
        }

        /// <summary>
        /// Load condition configuration from reference
        /// </summary>
        public static Condition LoadConditionFromReference(ConditionReference reference, string? environment = null)
        {
            var filePath = Path.Combine("Conditions", reference.Ref);
            
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Condition file not found: {filePath}");
            }

            try
            {
                var json = File.ReadAllText(filePath);
                var condition = JsonSerializer.Deserialize<Condition>(json);
                
                if (condition == null)
                {
                    throw new InvalidOperationException($"Failed to deserialize condition file: {filePath}");
                }

                // Apply parameter substitution
                if (reference.Params != null)
                {
                    ApplyParameterSubstitution(condition, reference.Params, environment);
                }

                return condition;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error loading condition from reference {reference.Ref}: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Create reusable condition configuration
        /// </summary>
        public static void CreateConditionConfiguration(ConditionConfiguration config)
        {
            Initialize();

            lock (_lock)
            {
                _conditionConfigurations[config.Name] = config;
                
                // Save to file
                SaveConditionConfiguration(config);
                
                Logger.Log($"Condition configuration '{config.Name}' created/updated", LogLevel.Info);
            }
        }

        /// <summary>
        /// Get all available condition configurations
        /// </summary>
        public static List<ConditionConfiguration> GetAllConditionConfigurations(string? environment = null)
        {
            Initialize();

            var configurations = new List<ConditionConfiguration>(_conditionConfigurations.Values);

            // Add environment-specific configurations
            if (!string.IsNullOrEmpty(environment) && 
                _environmentConditions.TryGetValue(environment, out var envConditions))
            {
                configurations.AddRange(envConditions.Values);
            }

            return configurations.Where(c => c.IsActive).ToList();
        }

        /// <summary>
        /// Validate condition configuration
        /// </summary>
        public static List<string> ValidateConditionConfiguration(ConditionConfiguration config)
        {
            var issues = new List<string>();

            if (string.IsNullOrEmpty(config.Name))
            {
                issues.Add("Condition name is required");
            }

            if (config.Condition == null)
            {
                issues.Add("Condition definition is required");
            }
            else
            {
                // Validate condition structure
                var conditionIssues = ValidateCondition(config.Condition);
                issues.AddRange(conditionIssues);
            }

            // Validate environment applicability
            if (config.ApplicableEnvironments?.Any() == true)
            {
                var validEnvironments = GetValidEnvironments();
                var invalidEnvs = config.ApplicableEnvironments.Except(validEnvironments).ToList();
                if (invalidEnvs.Any())
                {
                    issues.Add($"Invalid environments: {string.Join(", ", invalidEnvs)}");
                }
            }

            return issues;
        }

        /// <summary>
        /// Export condition configurations to JSON
        /// </summary>
        public static string ExportConditionConfigurations(string? environment = null)
        {
            var configurations = GetAllConditionConfigurations(environment);
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            return JsonSerializer.Serialize(configurations, options);
        }

        /// <summary>
        /// Import condition configurations from JSON
        /// </summary>
        public static void ImportConditionConfigurations(string json, bool overwriteExisting = false)
        {
            try
            {
                var configurations = JsonSerializer.Deserialize<List<ConditionConfiguration>>(json);
                if (configurations == null) return;

                foreach (var config in configurations)
                {
                    var issues = ValidateConditionConfiguration(config);
                    if (issues.Any())
                    {
                        Logger.Log($"Skipping invalid condition configuration '{config.Name}': {string.Join(", ", issues)}", LogLevel.Warn);
                        continue;
                    }

                    if (!overwriteExisting && _conditionConfigurations.ContainsKey(config.Name))
                    {
                        Logger.Log($"Skipping existing condition configuration '{config.Name}'", LogLevel.Info);
                        continue;
                    }

                    CreateConditionConfiguration(config);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error importing condition configurations: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        // Private helper methods
        private static void LoadGlobalConditions()
        {
            var conditionsDir = Path.Combine("Config", "Conditions");
            if (!Directory.Exists(conditionsDir)) return;

            var configFiles = Directory.GetFiles(conditionsDir, "*.json", SearchOption.TopDirectoryOnly);
            
            foreach (var configFile in configFiles)
            {
                try
                {
                    var json = File.ReadAllText(configFile);
                    var config = JsonSerializer.Deserialize<ConditionConfiguration>(json);
                    
                    if (config != null)
                    {
                        _conditionConfigurations[config.Name] = config;
                        Logger.Log($"Loaded global condition configuration: {config.Name}", LogLevel.Debug);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error loading condition configuration from {configFile}: {ex.Message}", LogLevel.Warn);
                }
            }
        }

        private static void LoadEnvironmentSpecificConditions()
        {
            var environmentsDir = Path.Combine("Config", "Conditions", "Environments");
            if (!Directory.Exists(environmentsDir)) return;

            var environmentDirs = Directory.GetDirectories(environmentsDir);
            
            foreach (var envDir in environmentDirs)
            {
                var environmentName = Path.GetFileName(envDir);
                var envConditions = new Dictionary<string, ConditionConfiguration>();

                var configFiles = Directory.GetFiles(envDir, "*.json", SearchOption.TopDirectoryOnly);
                
                foreach (var configFile in configFiles)
                {
                    try
                    {
                        var json = File.ReadAllText(configFile);
                        var config = JsonSerializer.Deserialize<ConditionConfiguration>(json);
                        
                        if (config != null)
                        {
                            envConditions[config.Name] = config;
                            Logger.Log($"Loaded environment condition configuration: {environmentName}.{config.Name}", LogLevel.Debug);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error loading environment condition configuration from {configFile}: {ex.Message}", LogLevel.Warn);
                    }
                }

                if (envConditions.Any())
                {
                    _environmentConditions[environmentName] = envConditions;
                }
            }
        }

        private static void SaveConditionConfiguration(ConditionConfiguration config)
        {
            try
            {
                var conditionsDir = Path.Combine("Config", "Conditions");
                Directory.CreateDirectory(conditionsDir);

                var filePath = Path.Combine(conditionsDir, $"{config.Name}.json");
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var json = JsonSerializer.Serialize(config, options);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error saving condition configuration '{config.Name}': {ex.Message}", LogLevel.Error);
            }
        }

        private static void ApplyParameterSubstitution(Condition condition, Dictionary<string, string> parameters, string? environment)
        {
            // Apply parameter substitution recursively
            if (condition.Predicate != null)
            {
                ApplyParameterSubstitution(condition.Predicate, parameters, environment);
            }

            if (condition.AnyOf != null)
            {
                foreach (var subCondition in condition.AnyOf)
                {
                    ApplyParameterSubstitution(subCondition, parameters, environment);
                }
            }

            if (condition.AllOf != null)
            {
                foreach (var subCondition in condition.AllOf)
                {
                    ApplyParameterSubstitution(subCondition, parameters, environment);
                }
            }

            if (condition.Not != null)
            {
                ApplyParameterSubstitution(condition.Not, parameters, environment);
            }

            // Apply environment-specific parameter overrides
            if (!string.IsNullOrEmpty(environment))
            {
                ApplyEnvironmentParameterOverrides(condition, environment);
            }
        }

        private static void ApplyParameterSubstitution(PredicateCondition predicate, Dictionary<string, string> parameters, string? environment)
        {
            if (predicate.Value != null)
            {
                foreach (var param in parameters)
                {
                    predicate.Value = predicate.Value.Replace($"{{{{{param.Key}}}}}", param.Value);
                }
            }
        }

        private static void ApplyEnvironmentParameterOverrides(Condition condition, string environment)
        {
            // Load environment-specific parameter overrides
            var overridesPath = Path.Combine("Config", "Conditions", "Parameters", $"{environment}.json");
            if (File.Exists(overridesPath))
            {
                try
                {
                    var json = File.ReadAllText(overridesPath);
                    var overrides = JsonSerializer.Deserialize<Dictionary<string, string>>(json);
                    
                    if (overrides != null)
                    {
                        ApplyParameterSubstitution(condition, overrides, null);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error applying environment parameter overrides for {environment}: {ex.Message}", LogLevel.Warn);
                }
            }
        }

        private static List<string> ValidateCondition(Condition condition)
        {
            var issues = new List<string>();

            // Count non-null condition types
            var conditionTypes = 0;
            if (condition.Predicate != null) conditionTypes++;
            if (condition.Reference != null) conditionTypes++;
            if (condition.AnyOf != null) conditionTypes++;
            if (condition.AllOf != null) conditionTypes++;
            if (condition.Not != null) conditionTypes++;
            if (condition.HoldUntil != null) conditionTypes++;
            if (condition.Retry != null) conditionTypes++;
            if (condition.Skip != null) conditionTypes++;
            if (condition.Environment != null) conditionTypes++;
            if (condition.FeatureFlag != null) conditionTypes++;

            if (conditionTypes == 0)
            {
                issues.Add("Condition must have at least one condition type defined");
            }
            else if (conditionTypes > 1)
            {
                issues.Add("Condition can only have one condition type defined");
            }

            // Validate specific condition types
            if (condition.Predicate != null)
            {
                if (string.IsNullOrEmpty(condition.Predicate.Context))
                {
                    issues.Add("Predicate condition must have a context");
                }
                if (string.IsNullOrEmpty(condition.Predicate.Operator))
                {
                    issues.Add("Predicate condition must have an operator");
                }
            }

            if (condition.Reference != null && string.IsNullOrEmpty(condition.Reference.Ref))
            {
                issues.Add("Reference condition must have a reference path");
            }

            return issues;
        }

        private static List<string> GetValidEnvironments()
        {
            return new List<string> { "UAT", "Production", "Pilot", "UAT61", "Development", "Staging" };
        }
    }
}
