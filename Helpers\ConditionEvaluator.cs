﻿using System.Text.Json;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    public static class ConditionEvaluator
    {
        /// <summary>
        /// Evaluate condition with enhanced support for new condition types
        /// </summary>
        public static bool Evaluate(Condition condition, IDictionary<string, object> context)
        {
            try
            {
                var result = EvaluateWithResult(condition, context);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating condition: {ex}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Evaluate condition and return detailed result
        /// </summary>
        public static ConditionResult EvaluateWithResult(Condition condition, IDictionary<string, object> context)
        {
            try
            {
                // Handle logical operators first
                if (condition.AnyOf != null)
                    return EvaluateAnyOf(condition.AnyOf, context);

                if (condition.AllOf != null)
                    return EvaluateAllOf(condition.AllOf, context);

                if (condition.Not != null)
                    return EvaluateNot(condition.Not, context);

                // Handle basic condition types
                if (condition.Predicate != null)
                    return EvaluatePredicateWithResult(condition.Predicate, context);

                if (condition.Reference != null)
                    return EvaluateReferenceWithResult(condition.Reference, context);

                // Handle new condition types
                if (condition.HoldUntil != null)
                    return ParallelConditionEvaluator.EvaluateHoldUntilCondition(condition.HoldUntil, context);

                if (condition.Retry != null)
                    return ConditionResult.Success("Retry condition configuration loaded",
                        new Dictionary<string, object> { ["retryConfig"] = condition.Retry });

                if (condition.Skip != null)
                    return EvaluateSkipCondition(condition.Skip, context);

                if (condition.Environment != null)
                    return EnvironmentConditionEvaluator.EvaluateEnvironmentCondition(condition.Environment, context);

                if (condition.FeatureFlag != null)
                    return EnvironmentConditionEvaluator.EvaluateFeatureFlagCondition(condition.FeatureFlag, context);

                Logger.Log("Condition was empty or unsupported.", LogLevel.Warn);
                return ConditionResult.Failure("Condition was empty or unsupported");
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating condition: {ex}", LogLevel.Error);
                return ConditionResult.Failure($"Condition evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Evaluate logical AnyOf condition
        /// </summary>
        private static ConditionResult EvaluateAnyOf(List<Condition> conditions, IDictionary<string, object> context)
        {
            var results = new List<ConditionResult>();

            foreach (var condition in conditions)
            {
                var result = EvaluateWithResult(condition, context);
                results.Add(result);

                if (result.IsSuccess)
                {
                    return ConditionResult.Success($"AnyOf condition satisfied by: {result.Message}",
                        new Dictionary<string, object> { ["results"] = results });
                }
            }

            return ConditionResult.Failure($"None of {conditions.Count} AnyOf conditions were satisfied",
                new Dictionary<string, object> { ["results"] = results });
        }

        /// <summary>
        /// Evaluate logical AllOf condition
        /// </summary>
        private static ConditionResult EvaluateAllOf(List<Condition> conditions, IDictionary<string, object> context)
        {
            var results = new List<ConditionResult>();

            foreach (var condition in conditions)
            {
                var result = EvaluateWithResult(condition, context);
                results.Add(result);

                if (!result.IsSuccess)
                {
                    return ConditionResult.Failure($"AllOf condition failed: {result.Message}",
                        new Dictionary<string, object> { ["results"] = results });
                }
            }

            return ConditionResult.Success($"All {conditions.Count} AllOf conditions satisfied",
                new Dictionary<string, object> { ["results"] = results });
        }

        /// <summary>
        /// Evaluate logical Not condition
        /// </summary>
        private static ConditionResult EvaluateNot(Condition condition, IDictionary<string, object> context)
        {
            var result = EvaluateWithResult(condition, context);

            if (result.IsSuccess)
            {
                return ConditionResult.Failure($"Not condition failed: inner condition succeeded ({result.Message})");
            }
            else
            {
                return ConditionResult.Success($"Not condition satisfied: inner condition failed ({result.Message})");
            }
        }

        /// <summary>
        /// Evaluate skip condition
        /// </summary>
        private static ConditionResult EvaluateSkipCondition(SkipCondition skipCondition, IDictionary<string, object> context)
        {
            if (skipCondition.When != null)
            {
                var whenResult = EvaluateWithResult(skipCondition.When, context);
                if (whenResult.IsSuccess)
                {
                    return ConditionResult.Success($"Skip condition met: {skipCondition.Reason ?? "No reason provided"}",
                        new Dictionary<string, object> { ["skip"] = true, ["reason"] = skipCondition.Reason ?? "" });
                }
            }

            return ConditionResult.Failure("Skip condition not met");
        }

        private static bool EvaluatePredicate(PredicateCondition predicate, IDictionary<string, object> context)
        {
            var result = EvaluatePredicateWithResult(predicate, context);
            return result.IsSuccess;
        }

        /// <summary>
        /// Evaluate predicate condition with detailed result
        /// </summary>
        private static ConditionResult EvaluatePredicateWithResult(PredicateCondition predicate, IDictionary<string, object> context)
        {
            try
            {
                object? valueFromContext = null;

                // Check if the context contains the specified key
                if (context.TryGetValue(predicate.Context, out var ctxObj))
                {
                    // If a field is specified, try to get the value from the context object
                    if (predicate.Field != null && ctxObj is IDictionary<string, object> dict)
                    {
                        dict.TryGetValue(predicate.Field, out valueFromContext);
                    }
                    else
                    {
                        valueFromContext = ctxObj;
                    }
                }

                var expected = predicate.Value;
                var actual = valueFromContext?.ToString();

                var result = predicate.Operator switch
                {
                    "Equals" => actual == expected,
                    "NotEquals" => actual != expected,
                    "Contains" => actual != null && actual.Contains(expected ?? "", StringComparison.OrdinalIgnoreCase),
                    "NotContains" => actual != null && !actual.Contains(expected ?? "", StringComparison.OrdinalIgnoreCase),
                    "GreaterThan" => CompareNumeric(actual, expected, (a, e) => a > e),
                    "LessThan" => CompareNumeric(actual, expected, (a, e) => a < e),
                    "GreaterThanOrEqual" => CompareNumeric(actual, expected, (a, e) => a >= e),
                    "LessThanOrEqual" => CompareNumeric(actual, expected, (a, e) => a <= e),
                    "StartsWith" => actual != null && actual.StartsWith(expected ?? "", StringComparison.OrdinalIgnoreCase),
                    "EndsWith" => actual != null && actual.EndsWith(expected ?? "", StringComparison.OrdinalIgnoreCase),
                    "IsNull" => actual == null,
                    "IsNotNull" => actual != null,
                    "IsEmpty" => string.IsNullOrEmpty(actual),
                    "IsNotEmpty" => !string.IsNullOrEmpty(actual),
                    _ => throw new InvalidOperationException($"Unsupported operator: {predicate.Operator}")
                };

                var message = $"Predicate {predicate.Context}.{predicate.Field} {predicate.Operator} '{expected}' -> {result} (actual: '{actual}')";

                return result
                    ? ConditionResult.Success(message, new Dictionary<string, object> { ["actual"] = actual ?? "", ["expected"] = expected ?? "" })
                    : ConditionResult.Failure(message, new Dictionary<string, object> { ["actual"] = actual ?? "", ["expected"] = expected ?? "" });
            }
            catch (Exception ex)
            {
                return ConditionResult.Failure($"Predicate evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Helper method for numeric comparison
        /// </summary>
        private static bool CompareNumeric(string? actual, string? expected, Func<double, double, bool> comparison)
        {
            if (double.TryParse(actual, out var actualNum) && double.TryParse(expected, out var expectedNum))
            {
                return comparison(actualNum, expectedNum);
            }
            return false;
        }

        private static bool EvaluateReference(ConditionReference reference, IDictionary<string, object> context)
        {
            var result = EvaluateReferenceWithResult(reference, context);
            return result.IsSuccess;
        }

        /// <summary>
        /// Evaluate reference condition with detailed result
        /// </summary>
        private static ConditionResult EvaluateReferenceWithResult(ConditionReference reference, IDictionary<string, object> context)
        {
            try
            {
                // Try to load from configuration manager first
                var environment = GetEnvironmentFromContext(context);
                var conditionConfig = ConditionConfigurationManager.GetConditionConfiguration(
                    Path.GetFileNameWithoutExtension(reference.Ref), environment);

                Condition condition;

                if (conditionConfig != null)
                {
                    condition = conditionConfig.Condition;
                    Logger.Log($"Loaded condition from configuration: {conditionConfig.Name}", LogLevel.Debug);
                }
                else
                {
                    // Fall back to direct file loading
                    condition = ConditionConfigurationManager.LoadConditionFromReference(reference, environment);
                    Logger.Log($"Loaded condition from file: {reference.Ref}", LogLevel.Debug);
                }

                // Apply parameter substitution
                if (reference.Params != null)
                {
                    ReplaceParams(condition, reference.Params);
                }

                var result = EvaluateWithResult(condition, context);
                return ConditionResult.Success($"Reference condition '{reference.Ref}' evaluated: {result.Message}",
                    new Dictionary<string, object> { ["referenceResult"] = result });
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating reference condition {reference.Ref}: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Reference condition evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get environment from context
        /// </summary>
        private static string? GetEnvironmentFromContext(IDictionary<string, object> context)
        {
            if (context.TryGetValue("Environment", out var envObj) &&
                envObj is Dictionary<string, object> envDict &&
                envDict.TryGetValue("Current", out var currentEnv))
            {
                return currentEnv?.ToString();
            }
            return null;
        }

        /// <summary>
        /// Replace parameter placeholders in condition
        /// </summary>
        private static void ReplaceParams(Condition condition, Dictionary<string, string> parameters)
        {
            if (condition.Predicate != null && condition.Predicate.Value != null)
            {
                foreach (var kv in parameters)
                {
                    condition.Predicate.Value = condition.Predicate.Value
                        .Replace($"{{{{{kv.Key}}}}}", kv.Value);
                }
            }

            // Handle new condition types
            if (condition.HoldUntil != null)
            {
                ReplaceParamsInHoldUntil(condition.HoldUntil, parameters);
            }

            if (condition.Environment != null)
            {
                ReplaceParamsInEnvironment(condition.Environment, parameters);
            }

            if (condition.FeatureFlag != null)
            {
                ReplaceParamsInFeatureFlag(condition.FeatureFlag, parameters);
            }

            // Handle nested conditions
            if (condition.AnyOf != null)
                foreach (var c in condition.AnyOf) ReplaceParams(c, parameters);

            if (condition.AllOf != null)
                foreach (var c in condition.AllOf) ReplaceParams(c, parameters);

            if (condition.Not != null)
                ReplaceParams(condition.Not, parameters);

            if (condition.Skip?.When != null)
                ReplaceParams(condition.Skip.When, parameters);
        }

        /// <summary>
        /// Replace parameters in HoldUntil condition
        /// </summary>
        private static void ReplaceParamsInHoldUntil(HoldUntilCondition holdUntil, Dictionary<string, string> parameters)
        {
            foreach (var param in parameters)
            {
                var placeholder = $"{{{{{param.Key}}}}}";

                if (holdUntil.TestCaseName != null)
                    holdUntil.TestCaseName = holdUntil.TestCaseName.Replace(placeholder, param.Value);

                if (holdUntil.TestCaseId != null)
                    holdUntil.TestCaseId = holdUntil.TestCaseId.Replace(placeholder, param.Value);

                if (holdUntil.ResourceName != null)
                    holdUntil.ResourceName = holdUntil.ResourceName.Replace(placeholder, param.Value);

                holdUntil.ExpectedStatus = holdUntil.ExpectedStatus.Replace(placeholder, param.Value);
            }
        }

        /// <summary>
        /// Replace parameters in Environment condition
        /// </summary>
        private static void ReplaceParamsInEnvironment(EnvironmentCondition environment, Dictionary<string, string> parameters)
        {
            foreach (var param in parameters)
            {
                var placeholder = $"{{{{{param.Key}}}}}";

                if (environment.TargetEnvironment != null)
                    environment.TargetEnvironment = environment.TargetEnvironment.Replace(placeholder, param.Value);
            }
        }

        /// <summary>
        /// Replace parameters in FeatureFlag condition
        /// </summary>
        private static void ReplaceParamsInFeatureFlag(FeatureFlagCondition featureFlag, Dictionary<string, string> parameters)
        {
            foreach (var param in parameters)
            {
                var placeholder = $"{{{{{param.Key}}}}}";
                featureFlag.FeatureName = featureFlag.FeatureName.Replace(placeholder, param.Value);
            }
        }

        /// <summary>
        /// Evaluate condition with centralized context management
        /// </summary>
        public static bool EvaluateWithContext(Condition condition, TestCase testCase, TestStep? currentStep = null)
        {
            var context = ContextManager.BuildContext(testCase, currentStep);
            return Evaluate(condition, context);
        }

        /// <summary>
        /// Evaluate condition with centralized context management and detailed result
        /// </summary>
        public static ConditionResult EvaluateWithContextAndResult(Condition condition, TestCase testCase, TestStep? currentStep = null)
        {
            var context = ContextManager.BuildContext(testCase, currentStep);
            return EvaluateWithResult(condition, context);
        }
    }
}
