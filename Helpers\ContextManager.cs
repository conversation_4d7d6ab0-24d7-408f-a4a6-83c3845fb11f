using System.Collections.Concurrent;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    /// <summary>
    /// Centralized context management for condition evaluation
    /// </summary>
    public static class ContextManager
    {
        private static readonly ConcurrentDictionary<string, object> _globalContext = new();
        private static readonly ConcurrentDictionary<string, TestExecutionState> _testExecutionStates = new();
        private static readonly ConcurrentDictionary<string, DateTime> _resourceLocks = new();
        private static readonly object _lockObject = new();

        /// <summary>
        /// Build comprehensive context for condition evaluation
        /// </summary>
        public static IDictionary<string, object> BuildContext(TestCase testCase, TestStep? currentStep = null)
        {
            var context = new Dictionary<string, object>();

            // Add environment context
            AddEnvironmentContext(context);

            // Add test execution context
            AddTestExecutionContext(context, testCase, currentStep);

            // Add runtime parameters
            AddRuntimeParameters(context);

            // Add feature flags
            AddFeatureFlags(context);

            // Add external system status
            AddExternalSystemStatus(context);

            // Add global context
            AddGlobalContext(context);

            return context;
        }

        /// <summary>
        /// Add environment-specific context
        /// </summary>
        private static void AddEnvironmentContext(Dictionary<string, object> context)
        {
            var runningInstructions = ConfigHelper.RunningInstructions;
            var currentEnvironment = runningInstructions?.RunningEnvironment ?? "UAT";

            context["Environment"] = new Dictionary<string, object>
            {
                ["Current"] = currentEnvironment,
                ["IsUAT"] = currentEnvironment.Equals("UAT", StringComparison.OrdinalIgnoreCase),
                ["IsProduction"] = currentEnvironment.Equals("Production", StringComparison.OrdinalIgnoreCase),
                ["IsPilot"] = currentEnvironment.Equals("Pilot", StringComparison.OrdinalIgnoreCase),
                ["BaseUrl"] = Config.Settings.BaseUrls.GetValueOrDefault(currentEnvironment, ""),
                ["Config"] = GetEnvironmentSpecificConfig(currentEnvironment)
            };
        }

        /// <summary>
        /// Add test execution state context
        /// </summary>
        private static void AddTestExecutionContext(Dictionary<string, object> context, TestCase testCase, TestStep? currentStep)
        {
            var testState = GetOrCreateTestState(testCase.TestCaseId.ToString());

            context["TestCase"] = new Dictionary<string, object>
            {
                ["Id"] = testCase.TestCaseId.ToString(),
                ["Name"] = testCase.TestCaseName,
                ["Code"] = testCase.TestCaseCode,
                ["Status"] = testState.Status,
                ["StartTime"] = testState.StartTime,
                ["CurrentStepIndex"] = testState.CurrentStepIndex,
                ["TotalSteps"] = testCase.Steps?.Count ?? 0,
                ["Labels"] = testCase.Labels ?? Array.Empty<string>(),
                ["Environment"] = testCase.Environment
            };

            if (currentStep != null)
            {
                context["Step"] = new Dictionary<string, object>
                {
                    ["Name"] = currentStep.Name,
                    ["Command"] = currentStep.Command,
                    ["Target"] = currentStep.Target,
                    ["Value"] = currentStep.Value,
                    ["Index"] = testState.CurrentStepIndex
                };
            }

            // Add parallel execution context
            context["ParallelExecution"] = new Dictionary<string, object>
            {
                ["RunningTests"] = GetRunningTestCases(),
                ["CompletedTests"] = GetCompletedTestCases(),
                ["LockedResources"] = GetLockedResources()
            };
        }

        /// <summary>
        /// Add runtime parameters context
        /// </summary>
        private static void AddRuntimeParameters(Dictionary<string, object> context)
        {
            var runningInstructions = ConfigHelper.RunningInstructions;

            context["Runtime"] = new Dictionary<string, object>
            {
                ["Browsers"] = runningInstructions?.RunningBrowsers ?? Array.Empty<string>(),
                ["MobileView"] = runningInstructions?.MobileView ?? false,
                ["RetryCount"] = runningInstructions?.RetryCount ?? 1,
                ["ExecutionTime"] = DateTime.Now,
                ["ThreadId"] = Thread.CurrentThread.ManagedThreadId,
                ["MachineName"] = Environment.MachineName
            };
        }

        /// <summary>
        /// Add feature flags context
        /// </summary>
        private static void AddFeatureFlags(Dictionary<string, object> context)
        {
            var environment = ((Dictionary<string, object>)context["Environment"])["Current"].ToString();
            var featureFlags = LoadFeatureFlags(environment);

            context["FeatureFlags"] = featureFlags;
        }

        /// <summary>
        /// Add external system status context
        /// </summary>
        private static void AddExternalSystemStatus(Dictionary<string, object> context)
        {
            context["ExternalSystems"] = new Dictionary<string, object>
            {
                ["DatabaseConnectivity"] = CheckDatabaseConnectivity(),
                ["ApiEndpoints"] = CheckApiEndpoints(),
                ["NetworkLatency"] = MeasureNetworkLatency()
            };
        }

        /// <summary>
        /// Add global context values
        /// </summary>
        private static void AddGlobalContext(Dictionary<string, object> context)
        {
            foreach (var kvp in _globalContext)
            {
                context[kvp.Key] = kvp.Value;
            }
        }

        /// <summary>
        /// Update test execution state
        /// </summary>
        public static void UpdateTestState(string testCaseId, string status, int? currentStepIndex = null)
        {
            var state = GetOrCreateTestState(testCaseId);
            state.Status = status;
            state.LastUpdated = DateTime.Now;
            
            if (currentStepIndex.HasValue)
            {
                state.CurrentStepIndex = currentStepIndex.Value;
            }

            _testExecutionStates.AddOrUpdate(testCaseId, state, (key, oldValue) => state);
        }

        /// <summary>
        /// Lock a resource for exclusive access
        /// </summary>
        public static bool TryLockResource(string resourceName, TimeSpan timeout)
        {
            lock (_lockObject)
            {
                if (_resourceLocks.ContainsKey(resourceName))
                {
                    var lockTime = _resourceLocks[resourceName];
                    if (DateTime.Now - lockTime < timeout)
                    {
                        return false; // Resource is still locked
                    }
                }

                _resourceLocks[resourceName] = DateTime.Now;
                return true;
            }
        }

        /// <summary>
        /// Release a resource lock
        /// </summary>
        public static void ReleaseResource(string resourceName)
        {
            lock (_lockObject)
            {
                _resourceLocks.TryRemove(resourceName, out _);
            }
        }

        /// <summary>
        /// Set global context value
        /// </summary>
        public static void SetGlobalContext(string key, object value)
        {
            _globalContext.AddOrUpdate(key, value, (k, v) => value);
        }

        /// <summary>
        /// Get global context value
        /// </summary>
        public static T? GetGlobalContext<T>(string key)
        {
            if (_globalContext.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default;
        }

        // Helper methods
        private static TestExecutionState GetOrCreateTestState(string testCaseId)
        {
            return _testExecutionStates.GetOrAdd(testCaseId, id => new TestExecutionState
            {
                TestCaseId = id,
                Status = "NotStarted",
                StartTime = DateTime.Now,
                CurrentStepIndex = 0
            });
        }

        private static Dictionary<string, object> GetEnvironmentSpecificConfig(string environment)
        {
            // Load environment-specific configuration
            var configPath = $"Config/{environment}.json";
            if (File.Exists(configPath))
            {
                try
                {
                    return ConfigHelper.LoadConfig<Dictionary<string, object>>(configPath, false) ?? new Dictionary<string, object>();
                }
                catch
                {
                    Logger.Log($"Failed to load environment config for {environment}", LogLevel.Warn);
                }
            }
            return new Dictionary<string, object>();
        }

        private static Dictionary<string, bool> LoadFeatureFlags(string environment)
        {
            var featureFlagsPath = $"Config/FeatureFlags.{environment}.json";
            if (File.Exists(featureFlagsPath))
            {
                try
                {
                    return ConfigHelper.LoadConfig<Dictionary<string, bool>>(featureFlagsPath, false) ?? new Dictionary<string, bool>();
                }
                catch
                {
                    Logger.Log($"Failed to load feature flags for {environment}", LogLevel.Warn);
                }
            }
            return new Dictionary<string, bool>();
        }

        private static List<string> GetRunningTestCases()
        {
            return _testExecutionStates.Values
                .Where(state => state.Status == "Running" || state.Status == "InProgress")
                .Select(state => state.TestCaseId)
                .ToList();
        }

        private static List<string> GetCompletedTestCases()
        {
            return _testExecutionStates.Values
                .Where(state => state.Status == "Completed" || state.Status == "Passed" || state.Status == "Failed")
                .Select(state => state.TestCaseId)
                .ToList();
        }

        private static List<string> GetLockedResources()
        {
            lock (_lockObject)
            {
                return _resourceLocks.Keys.ToList();
            }
        }

        private static bool CheckDatabaseConnectivity()
        {
            // Implement database connectivity check
            // This is a placeholder - implement based on your database setup
            return true;
        }

        private static Dictionary<string, bool> CheckApiEndpoints()
        {
            // Implement API endpoint health checks
            // This is a placeholder - implement based on your API endpoints
            return new Dictionary<string, bool>
            {
                ["MainAPI"] = true,
                ["AuthAPI"] = true
            };
        }

        private static int MeasureNetworkLatency()
        {
            // Implement network latency measurement
            // This is a placeholder - implement based on your requirements
            return 50; // milliseconds
        }
    }

    /// <summary>
    /// Represents the execution state of a test case
    /// </summary>
    public class TestExecutionState
    {
        public string TestCaseId { get; set; } = string.Empty;
        public string Status { get; set; } = "NotStarted";
        public DateTime StartTime { get; set; }
        public DateTime LastUpdated { get; set; }
        public int CurrentStepIndex { get; set; }
        public Dictionary<string, object> CustomData { get; set; } = new();
    }
}
