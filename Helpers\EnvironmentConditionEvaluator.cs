using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    /// <summary>
    /// Evaluates environment-based conditions and feature flags
    /// </summary>
    public static class EnvironmentConditionEvaluator
    {
        /// <summary>
        /// Evaluate environment condition
        /// </summary>
        public static ConditionResult EvaluateEnvironmentCondition(EnvironmentCondition envCondition, IDictionary<string, object> context)
        {
            try
            {
                var currentEnvironment = GetCurrentEnvironment(context);
                
                // Check if condition applies to current environment
                if (!string.IsNullOrEmpty(envCondition.TargetEnvironment))
                {
                    var matches = envCondition.TargetEnvironment.Equals(currentEnvironment, StringComparison.OrdinalIgnoreCase);
                    return matches 
                        ? ConditionResult.Success($"Environment matches target: {envCondition.TargetEnvironment}")
                        : ConditionResult.Failure($"Environment {currentEnvironment} does not match target {envCondition.TargetEnvironment}");
                }

                // Check if current environment is in the list
                if (envCondition.Environments != null && envCondition.Environments.Any())
                {
                    var matches = envCondition.Environments.Any(env => 
                        env.Equals(currentEnvironment, StringComparison.OrdinalIgnoreCase));
                    
                    return matches 
                        ? ConditionResult.Success($"Environment {currentEnvironment} found in allowed list")
                        : ConditionResult.Failure($"Environment {currentEnvironment} not in allowed list: {string.Join(", ", envCondition.Environments)}");
                }

                // Apply environment-specific configuration
                if (envCondition.EnvironmentConfig != null && 
                    envCondition.EnvironmentConfig.TryGetValue(currentEnvironment, out var config))
                {
                    return ConditionResult.Success($"Environment configuration found for {currentEnvironment}", 
                        new Dictionary<string, object> { ["config"] = config });
                }

                return ConditionResult.Success("Environment condition passed with no specific constraints");
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating environment condition: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Environment condition evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Evaluate feature flag condition
        /// </summary>
        public static ConditionResult EvaluateFeatureFlagCondition(FeatureFlagCondition flagCondition, IDictionary<string, object> context)
        {
            try
            {
                var currentEnvironment = GetCurrentEnvironment(context);
                var featureFlags = GetFeatureFlags(context);
                
                // Check environment-specific override first
                if (flagCondition.EnvironmentOverrides != null && 
                    flagCondition.EnvironmentOverrides.TryGetValue(currentEnvironment, out var overrideValue))
                {
                    var matches = overrideValue == flagCondition.ExpectedValue;
                    return matches 
                        ? ConditionResult.Success($"Feature flag '{flagCondition.FeatureName}' environment override matches expected value: {flagCondition.ExpectedValue}")
                        : ConditionResult.Failure($"Feature flag '{flagCondition.FeatureName}' environment override ({overrideValue}) does not match expected value ({flagCondition.ExpectedValue})");
                }

                // Check global feature flag value
                if (featureFlags.TryGetValue(flagCondition.FeatureName, out var flagValue))
                {
                    var matches = flagValue == flagCondition.ExpectedValue;
                    return matches 
                        ? ConditionResult.Success($"Feature flag '{flagCondition.FeatureName}' matches expected value: {flagCondition.ExpectedValue}")
                        : ConditionResult.Failure($"Feature flag '{flagCondition.FeatureName}' ({flagValue}) does not match expected value ({flagCondition.ExpectedValue})");
                }

                // Feature flag not found - use default behavior
                var defaultMatches = false == flagCondition.ExpectedValue; // Default to false if not found
                return defaultMatches 
                    ? ConditionResult.Success($"Feature flag '{flagCondition.FeatureName}' not found, defaulting to false which matches expected value")
                    : ConditionResult.Failure($"Feature flag '{flagCondition.FeatureName}' not found, defaulting to false which does not match expected value ({flagCondition.ExpectedValue})");
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating feature flag condition: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Feature flag condition evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get environment-specific behavior configuration
        /// </summary>
        public static Dictionary<string, object> GetEnvironmentBehavior(string environment, string behaviorKey)
        {
            try
            {
                var configPath = Path.Combine("Config", "Environments", $"{environment}.json");
                if (File.Exists(configPath))
                {
                    var envConfig = ConfigHelper.LoadConfig<EnvironmentConfig>(configPath, false);
                    if (envConfig?.Settings.TryGetValue(behaviorKey, out var behavior) == true && behavior is Dictionary<string, object> behaviorDict)
                    {
                        return behaviorDict;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error loading environment behavior for {environment}.{behaviorKey}: {ex.Message}", LogLevel.Warn);
            }

            return new Dictionary<string, object>();
        }

        /// <summary>
        /// Check if feature is enabled for current environment
        /// </summary>
        public static bool IsFeatureEnabled(string featureName, IDictionary<string, object> context)
        {
            var condition = new FeatureFlagCondition
            {
                FeatureName = featureName,
                ExpectedValue = true
            };

            var result = EvaluateFeatureFlagCondition(condition, context);
            return result.IsSuccess;
        }

        /// <summary>
        /// Get all enabled features for current environment
        /// </summary>
        public static List<string> GetEnabledFeatures(IDictionary<string, object> context)
        {
            var featureFlags = GetFeatureFlags(context);
            return featureFlags.Where(kvp => kvp.Value).Select(kvp => kvp.Key).ToList();
        }

        /// <summary>
        /// Load environment-specific test data
        /// </summary>
        public static Dictionary<string, object> LoadEnvironmentTestData(string environment, string dataKey)
        {
            try
            {
                var dataPath = Path.Combine("TestData", "Environments", environment, $"{dataKey}.json");
                if (File.Exists(dataPath))
                {
                    return ConfigHelper.LoadConfig<Dictionary<string, object>>(dataPath, false) ?? new Dictionary<string, object>();
                }

                // Fallback to default environment data
                var defaultPath = Path.Combine("TestData", "Environments", "Default", $"{dataKey}.json");
                if (File.Exists(defaultPath))
                {
                    return ConfigHelper.LoadConfig<Dictionary<string, object>>(defaultPath, false) ?? new Dictionary<string, object>();
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error loading environment test data for {environment}.{dataKey}: {ex.Message}", LogLevel.Warn);
            }

            return new Dictionary<string, object>();
        }

        /// <summary>
        /// Validate environment configuration
        /// </summary>
        public static List<string> ValidateEnvironmentConfiguration(string environment)
        {
            var issues = new List<string>();

            try
            {
                // Check if environment configuration exists
                var configPath = Path.Combine("Config", "Environments", $"{environment}.json");
                if (!File.Exists(configPath))
                {
                    issues.Add($"Environment configuration file not found: {configPath}");
                    return issues;
                }

                // Load and validate configuration
                var envConfig = ConfigHelper.LoadConfig<EnvironmentConfig>(configPath, false);
                if (envConfig == null)
                {
                    issues.Add($"Failed to load environment configuration for {environment}");
                    return issues;
                }

                // Validate required settings
                if (string.IsNullOrEmpty(envConfig.Name))
                {
                    issues.Add("Environment name is required");
                }

                // Validate connection strings
                foreach (var connString in envConfig.ConnectionStrings)
                {
                    if (string.IsNullOrEmpty(connString.Value))
                    {
                        issues.Add($"Connection string '{connString.Key}' is empty");
                    }
                }

                // Validate API endpoints
                foreach (var endpoint in envConfig.ApiEndpoints)
                {
                    if (string.IsNullOrEmpty(endpoint.Value) || !Uri.IsWellFormedUriString(endpoint.Value, UriKind.Absolute))
                    {
                        issues.Add($"API endpoint '{endpoint.Key}' is invalid: {endpoint.Value}");
                    }
                }

                // Validate feature flags
                foreach (var featureFlag in envConfig.FeatureFlags)
                {
                    if (string.IsNullOrEmpty(featureFlag.Name))
                    {
                        issues.Add("Feature flag name cannot be empty");
                    }

                    if (featureFlag.ExpirationDate.HasValue && featureFlag.ExpirationDate < DateTime.Now)
                    {
                        issues.Add($"Feature flag '{featureFlag.Name}' has expired");
                    }
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Error validating environment configuration: {ex.Message}");
            }

            return issues;
        }

        // Helper methods
        private static string GetCurrentEnvironment(IDictionary<string, object> context)
        {
            if (context.TryGetValue("Environment", out var envObj) && 
                envObj is Dictionary<string, object> envDict &&
                envDict.TryGetValue("Current", out var currentEnv))
            {
                return currentEnv?.ToString() ?? "UAT";
            }
            return "UAT";
        }

        private static Dictionary<string, bool> GetFeatureFlags(IDictionary<string, object> context)
        {
            if (context.TryGetValue("FeatureFlags", out var flagsObj) && 
                flagsObj is Dictionary<string, bool> flags)
            {
                return flags;
            }
            return new Dictionary<string, bool>();
        }
    }
}
