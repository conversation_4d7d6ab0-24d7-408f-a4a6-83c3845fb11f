using System.Collections.Concurrent;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    /// <summary>
    /// Evaluates parallel execution conditions and manages test coordination
    /// </summary>
    public static class ParallelConditionEvaluator
    {
        private static readonly ConcurrentDictionary<string, TestExecutionState> _testStates = new();
        private static readonly ConcurrentDictionary<string, ResourceLock> _resourceLocks = new();
        private static readonly ConcurrentDictionary<string, ManualResetEventSlim> _testCompletionEvents = new();
        private static readonly object _lockObject = new();

        /// <summary>
        /// Evaluate hold until condition
        /// </summary>
        public static ConditionResult EvaluateHoldUntilCondition(HoldUntilCondition holdCondition, IDictionary<string, object> context)
        {
            try
            {
                var startTime = DateTime.Now;

                // Wait for test case completion
                if (!string.IsNullOrEmpty(holdCondition.TestCaseId) || !string.IsNullOrEmpty(holdCondition.TestCaseName))
                {
                    var result = WaitForTestCompletion(holdCondition, startTime);
                    if (!result.IsSuccess)
                        return result;
                }

                // Wait for resource availability
                if (!string.IsNullOrEmpty(holdCondition.ResourceName))
                {
                    var result = WaitForResourceAvailability(holdCondition, startTime);
                    if (!result.IsSuccess)
                        return result;
                }

                var executionTime = DateTime.Now - startTime;
                return ConditionResult.Success($"Hold condition satisfied", 
                    new Dictionary<string, object> { ["executionTime"] = executionTime });
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating hold until condition: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Hold until condition evaluation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Wait for test case completion
        /// </summary>
        private static ConditionResult WaitForTestCompletion(HoldUntilCondition holdCondition, DateTime startTime)
        {
            var testId = holdCondition.TestCaseId ?? holdCondition.TestCaseName ?? "";
            var maxWaitTime = TimeSpan.FromMilliseconds(holdCondition.MaxWaitTimeMs);
            var pollingInterval = TimeSpan.FromMilliseconds(holdCondition.PollingIntervalMs);

            Logger.Log($"Waiting for test completion: {testId}, Expected Status: {holdCondition.ExpectedStatus}", LogLevel.Info);

            while (DateTime.Now - startTime < maxWaitTime)
            {
                // Check if test is completed with expected status
                if (_testStates.TryGetValue(testId, out var testState))
                {
                    if (testState.Status.Equals(holdCondition.ExpectedStatus, StringComparison.OrdinalIgnoreCase))
                    {
                        Logger.Log($"Test {testId} completed with expected status: {holdCondition.ExpectedStatus}", LogLevel.Info);
                        return ConditionResult.Success($"Test {testId} completed with status {testState.Status}");
                    }

                    // Check for failure states
                    if (testState.Status.Equals("Failed", StringComparison.OrdinalIgnoreCase) ||
                        testState.Status.Equals("Error", StringComparison.OrdinalIgnoreCase))
                    {
                        Logger.Log($"Test {testId} failed with status: {testState.Status}", LogLevel.Warn);
                        return ConditionResult.Failure($"Test {testId} failed with status {testState.Status}");
                    }
                }

                // Wait before next check
                Thread.Sleep(pollingInterval);
            }

            return ConditionResult.Failure($"Timeout waiting for test {testId} to complete with status {holdCondition.ExpectedStatus}");
        }

        /// <summary>
        /// Wait for resource availability
        /// </summary>
        private static ConditionResult WaitForResourceAvailability(HoldUntilCondition holdCondition, DateTime startTime)
        {
            var resourceName = holdCondition.ResourceName!;
            var maxWaitTime = TimeSpan.FromMilliseconds(holdCondition.MaxWaitTimeMs);
            var pollingInterval = TimeSpan.FromMilliseconds(holdCondition.PollingIntervalMs);

            Logger.Log($"Waiting for resource availability: {resourceName}", LogLevel.Info);

            while (DateTime.Now - startTime < maxWaitTime)
            {
                if (!_resourceLocks.ContainsKey(resourceName))
                {
                    Logger.Log($"Resource {resourceName} is available", LogLevel.Info);
                    return ConditionResult.Success($"Resource {resourceName} is available");
                }

                // Check if lock has expired
                if (_resourceLocks.TryGetValue(resourceName, out var resourceLock))
                {
                    if (DateTime.Now > resourceLock.ExpirationTime)
                    {
                        // Remove expired lock
                        _resourceLocks.TryRemove(resourceName, out _);
                        Logger.Log($"Expired lock removed for resource: {resourceName}", LogLevel.Info);
                        return ConditionResult.Success($"Resource {resourceName} is available (expired lock removed)");
                    }
                }

                // Wait before next check
                Thread.Sleep(pollingInterval);
            }

            return ConditionResult.Failure($"Timeout waiting for resource {resourceName} to become available");
        }

        /// <summary>
        /// Lock a resource for exclusive access
        /// </summary>
        public static ConditionResult LockResource(string resourceName, TimeSpan lockDuration, string testCaseId, string? reason = null)
        {
            try
            {
                lock (_lockObject)
                {
                    // Check if resource is already locked
                    if (_resourceLocks.TryGetValue(resourceName, out var existingLock))
                    {
                        if (DateTime.Now < existingLock.ExpirationTime)
                        {
                            return ConditionResult.Failure($"Resource {resourceName} is already locked by {existingLock.OwnerTestCaseId} until {existingLock.ExpirationTime}");
                        }
                        else
                        {
                            // Remove expired lock
                            _resourceLocks.TryRemove(resourceName, out _);
                        }
                    }

                    // Create new lock
                    var resourceLock = new ResourceLock
                    {
                        ResourceName = resourceName,
                        OwnerTestCaseId = testCaseId,
                        LockTime = DateTime.Now,
                        ExpirationTime = DateTime.Now.Add(lockDuration),
                        Reason = reason
                    };

                    _resourceLocks[resourceName] = resourceLock;
                    Logger.Log($"Resource {resourceName} locked by {testCaseId} until {resourceLock.ExpirationTime}", LogLevel.Info);
                    
                    return ConditionResult.Success($"Resource {resourceName} locked successfully", 
                        new Dictionary<string, object> { ["lock"] = resourceLock });
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error locking resource {resourceName}: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Failed to lock resource {resourceName}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Release a resource lock
        /// </summary>
        public static ConditionResult ReleaseResource(string resourceName, string testCaseId)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_resourceLocks.TryGetValue(resourceName, out var resourceLock))
                    {
                        // Verify ownership
                        if (!resourceLock.OwnerTestCaseId.Equals(testCaseId, StringComparison.OrdinalIgnoreCase))
                        {
                            return ConditionResult.Failure($"Cannot release resource {resourceName}: owned by {resourceLock.OwnerTestCaseId}, not {testCaseId}");
                        }

                        _resourceLocks.TryRemove(resourceName, out _);
                        Logger.Log($"Resource {resourceName} released by {testCaseId}", LogLevel.Info);
                        
                        return ConditionResult.Success($"Resource {resourceName} released successfully");
                    }
                    else
                    {
                        return ConditionResult.Success($"Resource {resourceName} was not locked");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error releasing resource {resourceName}: {ex.Message}", LogLevel.Error);
                return ConditionResult.Failure($"Failed to release resource {resourceName}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Update test execution state
        /// </summary>
        public static void UpdateTestState(string testCaseId, string status, int? currentStepIndex = null, Dictionary<string, object>? customData = null)
        {
            var state = _testStates.GetOrAdd(testCaseId, id => new TestExecutionState
            {
                TestCaseId = id,
                StartTime = DateTime.Now
            });

            state.Status = status;
            state.LastUpdated = DateTime.Now;
            
            if (currentStepIndex.HasValue)
            {
                state.CurrentStepIndex = currentStepIndex.Value;
            }

            if (customData != null)
            {
                foreach (var kvp in customData)
                {
                    state.CustomData[kvp.Key] = kvp.Value;
                }
            }

            // Notify waiting conditions
            if (_testCompletionEvents.TryGetValue(testCaseId, out var eventSlim))
            {
                eventSlim.Set();
            }

            Logger.Log($"Test state updated: {testCaseId} -> {status}", LogLevel.Debug);
        }

        /// <summary>
        /// Get test execution state
        /// </summary>
        public static TestExecutionState? GetTestState(string testCaseId)
        {
            return _testStates.TryGetValue(testCaseId, out var state) ? state : null;
        }

        /// <summary>
        /// Get all running tests
        /// </summary>
        public static List<TestExecutionState> GetRunningTests()
        {
            return _testStates.Values
                .Where(state => state.Status.Equals("Running", StringComparison.OrdinalIgnoreCase) ||
                               state.Status.Equals("InProgress", StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        /// <summary>
        /// Get all locked resources
        /// </summary>
        public static List<ResourceLock> GetLockedResources()
        {
            var now = DateTime.Now;
            var activeLocks = new List<ResourceLock>();

            foreach (var kvp in _resourceLocks)
            {
                if (kvp.Value.ExpirationTime > now)
                {
                    activeLocks.Add(kvp.Value);
                }
                else
                {
                    // Remove expired lock
                    _resourceLocks.TryRemove(kvp.Key, out _);
                }
            }

            return activeLocks;
        }

        /// <summary>
        /// Clean up expired locks and completed test states
        /// </summary>
        public static void Cleanup()
        {
            var now = DateTime.Now;
            var expiredLocks = new List<string>();
            var completedTests = new List<string>();

            // Clean up expired resource locks
            foreach (var kvp in _resourceLocks)
            {
                if (kvp.Value.ExpirationTime <= now)
                {
                    expiredLocks.Add(kvp.Key);
                }
            }

            foreach (var resourceName in expiredLocks)
            {
                _resourceLocks.TryRemove(resourceName, out _);
                Logger.Log($"Expired resource lock removed: {resourceName}", LogLevel.Debug);
            }

            // Clean up old completed test states (older than 1 hour)
            var cutoffTime = now.AddHours(-1);
            foreach (var kvp in _testStates)
            {
                if (kvp.Value.LastUpdated < cutoffTime && 
                    (kvp.Value.Status.Equals("Completed", StringComparison.OrdinalIgnoreCase) ||
                     kvp.Value.Status.Equals("Failed", StringComparison.OrdinalIgnoreCase)))
                {
                    completedTests.Add(kvp.Key);
                }
            }

            foreach (var testId in completedTests)
            {
                _testStates.TryRemove(testId, out _);
                
                // Clean up completion events
                if (_testCompletionEvents.TryRemove(testId, out var eventSlim))
                {
                    eventSlim.Dispose();
                }
                
                Logger.Log($"Old test state removed: {testId}", LogLevel.Debug);
            }
        }

        /// <summary>
        /// Get coordination statistics
        /// </summary>
        public static Dictionary<string, object> GetCoordinationStatistics()
        {
            return new Dictionary<string, object>
            {
                ["ActiveTests"] = _testStates.Count,
                ["RunningTests"] = GetRunningTests().Count,
                ["LockedResources"] = GetLockedResources().Count,
                ["CompletionEvents"] = _testCompletionEvents.Count
            };
        }
    }

    /// <summary>
    /// Represents a resource lock
    /// </summary>
    public class ResourceLock
    {
        public string ResourceName { get; set; } = string.Empty;
        public string OwnerTestCaseId { get; set; } = string.Empty;
        public DateTime LockTime { get; set; }
        public DateTime ExpirationTime { get; set; }
        public string? Reason { get; set; }
    }
}
