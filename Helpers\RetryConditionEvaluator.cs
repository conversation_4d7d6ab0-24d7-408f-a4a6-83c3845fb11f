using OpenQA.Selenium;
using TestAutomationFramework.Models;

namespace TestAutomationFramework.Helpers
{
    /// <summary>
    /// Evaluates retry conditions and manages step-level retry logic
    /// </summary>
    public static class RetryConditionEvaluator
    {
        /// <summary>
        /// Evaluate retry condition and execute with retry logic
        /// </summary>
        public static ConditionResult EvaluateRetryCondition(RetryCondition retryCondition, Func<ConditionResult> operation, IDictionary<string, object> context)
        {
            var startTime = DateTime.Now;
            var attempt = 0;
            var currentDelay = retryCondition.RetryDelayMs;
            Exception? lastException = null;

            Logger.Log($"Starting retry operation with max {retryCondition.MaxRetries} attempts", LogLevel.Info);

            while (attempt < retryCondition.MaxRetries)
            {
                attempt++;
                
                try
                {
                    Logger.Log($"Retry attempt {attempt}/{retryCondition.MaxRetries}", LogLevel.Debug);
                    
                    var result = operation();
                    
                    if (result.IsSuccess)
                    {
                        var executionTime = DateTime.Now - startTime;
                        Logger.Log($"Operation succeeded on attempt {attempt} after {executionTime.TotalMilliseconds}ms", LogLevel.Info);
                        
                        return ConditionResult.Success($"Operation succeeded on attempt {attempt}", 
                            new Dictionary<string, object> 
                            { 
                                ["attempt"] = attempt,
                                ["executionTime"] = executionTime
                            });
                    }

                    // Check if we should retry based on the result
                    if (retryCondition.RetryWhen != null)
                    {
                        var shouldRetry = ConditionEvaluator.Evaluate(retryCondition.RetryWhen, context);
                        if (!shouldRetry)
                        {
                            Logger.Log($"Retry condition not met, stopping retries after attempt {attempt}", LogLevel.Info);
                            return result;
                        }
                    }

                    lastException = result.Exception;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    Logger.Log($"Exception on attempt {attempt}: {ex.Message}", LogLevel.Warn);

                    // Check if this exception type should trigger retry
                    if (!ShouldRetryOnException(ex, retryCondition))
                    {
                        Logger.Log($"Exception type {ex.GetType().Name} not configured for retry", LogLevel.Info);
                        return ConditionResult.Failure($"Operation failed with non-retryable exception: {ex.Message}", ex);
                    }
                }

                // Don't wait after the last attempt
                if (attempt < retryCondition.MaxRetries)
                {
                    Logger.Log($"Waiting {currentDelay}ms before next attempt", LogLevel.Debug);
                    Thread.Sleep(currentDelay);
                    
                    // Apply exponential backoff
                    currentDelay = (int)(currentDelay * retryCondition.BackoffMultiplier);
                }
            }

            var totalExecutionTime = DateTime.Now - startTime;
            var errorMessage = $"Operation failed after {retryCondition.MaxRetries} attempts over {totalExecutionTime.TotalSeconds:F2} seconds";
            
            Logger.Log(errorMessage, LogLevel.Error);
            return ConditionResult.Failure(errorMessage, lastException);
        }

        /// <summary>
        /// Execute a test step with retry logic
        /// </summary>
        public static ConditionResult ExecuteStepWithRetry(TestStep step, IWebDriver driver, RetryCondition retryCondition)
        {
            return EvaluateRetryCondition(retryCondition, () =>
            {
                try
                {
                    WebDriverHelper.ExecuteStep(driver, step);
                    return ConditionResult.Success($"Step '{step.Name}' executed successfully");
                }
                catch (Exception ex)
                {
                    return ConditionResult.Failure($"Step '{step.Name}' failed: {ex.Message}", ex);
                }
            }, new Dictionary<string, object>());
        }

        /// <summary>
        /// Create retry condition for network timeouts
        /// </summary>
        public static RetryCondition CreateNetworkTimeoutRetry(int maxRetries = 3, int initialDelayMs = 1000)
        {
            return new RetryCondition
            {
                MaxRetries = maxRetries,
                RetryDelayMs = initialDelayMs,
                BackoffMultiplier = 1.5,
                RetryOnExceptions = new List<string>
                {
                    typeof(WebDriverTimeoutException).Name,
                    typeof(TimeoutException).Name,
                    typeof(HttpRequestException).Name,
                    typeof(SocketException).Name
                }
            };
        }

        /// <summary>
        /// Create retry condition for flaky UI interactions
        /// </summary>
        public static RetryCondition CreateUIInteractionRetry(int maxRetries = 5, int initialDelayMs = 500)
        {
            return new RetryCondition
            {
                MaxRetries = maxRetries,
                RetryDelayMs = initialDelayMs,
                BackoffMultiplier = 1.2,
                RetryOnExceptions = new List<string>
                {
                    typeof(StaleElementReferenceException).Name,
                    typeof(ElementNotInteractableException).Name,
                    typeof(ElementClickInterceptedException).Name,
                    typeof(NoSuchElementException).Name,
                    typeof(InvalidElementStateException).Name
                }
            };
        }

        /// <summary>
        /// Create retry condition for external service failures
        /// </summary>
        public static RetryCondition CreateExternalServiceRetry(int maxRetries = 3, int initialDelayMs = 2000)
        {
            return new RetryCondition
            {
                MaxRetries = maxRetries,
                RetryDelayMs = initialDelayMs,
                BackoffMultiplier = 2.0,
                RetryOnExceptions = new List<string>
                {
                    typeof(HttpRequestException).Name,
                    typeof(TaskCanceledException).Name,
                    typeof(WebException).Name,
                    typeof(InvalidOperationException).Name
                }
            };
        }

        /// <summary>
        /// Create conditional retry based on specific conditions
        /// </summary>
        public static RetryCondition CreateConditionalRetry(Condition retryWhen, int maxRetries = 3, int initialDelayMs = 1000)
        {
            return new RetryCondition
            {
                MaxRetries = maxRetries,
                RetryDelayMs = initialDelayMs,
                BackoffMultiplier = 1.5,
                RetryWhen = retryWhen
            };
        }

        /// <summary>
        /// Execute operation with circuit breaker pattern
        /// </summary>
        public static ConditionResult ExecuteWithCircuitBreaker(Func<ConditionResult> operation, string circuitName, 
            int failureThreshold = 5, TimeSpan recoveryTimeout = default)
        {
            if (recoveryTimeout == default)
                recoveryTimeout = TimeSpan.FromMinutes(1);

            var circuitState = CircuitBreakerManager.GetCircuitState(circuitName);
            
            // Check if circuit is open
            if (circuitState.IsOpen && DateTime.Now < circuitState.NextAttemptTime)
            {
                return ConditionResult.Failure($"Circuit breaker '{circuitName}' is open. Next attempt allowed at {circuitState.NextAttemptTime}");
            }

            try
            {
                var result = operation();
                
                if (result.IsSuccess)
                {
                    // Reset circuit breaker on success
                    CircuitBreakerManager.RecordSuccess(circuitName);
                    return result;
                }
                else
                {
                    // Record failure
                    CircuitBreakerManager.RecordFailure(circuitName, failureThreshold, recoveryTimeout);
                    return result;
                }
            }
            catch (Exception ex)
            {
                // Record failure and re-throw
                CircuitBreakerManager.RecordFailure(circuitName, failureThreshold, recoveryTimeout);
                return ConditionResult.Failure($"Circuit breaker operation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Check if exception should trigger retry
        /// </summary>
        private static bool ShouldRetryOnException(Exception exception, RetryCondition retryCondition)
        {
            if (retryCondition.RetryOnExceptions == null || !retryCondition.RetryOnExceptions.Any())
            {
                // Default retryable exceptions
                return IsDefaultRetryableException(exception);
            }

            var exceptionTypeName = exception.GetType().Name;
            return retryCondition.RetryOnExceptions.Contains(exceptionTypeName);
        }

        /// <summary>
        /// Check if exception is retryable by default
        /// </summary>
        private static bool IsDefaultRetryableException(Exception exception)
        {
            return exception is WebDriverTimeoutException ||
                   exception is StaleElementReferenceException ||
                   exception is ElementNotInteractableException ||
                   exception is NoSuchElementException ||
                   exception is TimeoutException ||
                   exception is HttpRequestException ||
                   exception is TaskCanceledException;
        }
    }

    /// <summary>
    /// Circuit breaker manager for handling repeated failures
    /// </summary>
    public static class CircuitBreakerManager
    {
        private static readonly Dictionary<string, CircuitBreakerState> _circuitStates = new();
        private static readonly object _lock = new();

        public static CircuitBreakerState GetCircuitState(string circuitName)
        {
            lock (_lock)
            {
                if (!_circuitStates.TryGetValue(circuitName, out var state))
                {
                    state = new CircuitBreakerState { CircuitName = circuitName };
                    _circuitStates[circuitName] = state;
                }
                return state;
            }
        }

        public static void RecordSuccess(string circuitName)
        {
            lock (_lock)
            {
                if (_circuitStates.TryGetValue(circuitName, out var state))
                {
                    state.FailureCount = 0;
                    state.IsOpen = false;
                    state.LastSuccessTime = DateTime.Now;
                }
            }
        }

        public static void RecordFailure(string circuitName, int failureThreshold, TimeSpan recoveryTimeout)
        {
            lock (_lock)
            {
                var state = GetCircuitState(circuitName);
                state.FailureCount++;
                state.LastFailureTime = DateTime.Now;

                if (state.FailureCount >= failureThreshold)
                {
                    state.IsOpen = true;
                    state.NextAttemptTime = DateTime.Now.Add(recoveryTimeout);
                    Logger.Log($"Circuit breaker '{circuitName}' opened after {state.FailureCount} failures", LogLevel.Warn);
                }
            }
        }
    }

    /// <summary>
    /// Circuit breaker state
    /// </summary>
    public class CircuitBreakerState
    {
        public string CircuitName { get; set; } = string.Empty;
        public int FailureCount { get; set; }
        public bool IsOpen { get; set; }
        public DateTime NextAttemptTime { get; set; }
        public DateTime LastFailureTime { get; set; }
        public DateTime LastSuccessTime { get; set; }
    }
}
