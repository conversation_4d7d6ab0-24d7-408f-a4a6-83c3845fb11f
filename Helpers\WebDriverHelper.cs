using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Firefox;
using OpenQA.Selenium.Support.UI;
using WebDriverManager;
using WebDriverManager.DriverConfigs.Impl;
using TestAutomationFramework.Models;
using SeleniumExtras.WaitHelpers;
using NUnit.Framework;
using ICSharpCode.SharpZipLib;
using OpenQA.Selenium.DevTools.V130.Network;
using TestAutomationFramework.Exceptions;
using System.Collections.Immutable;
using System.Diagnostics;
using AngleSharp.Text;
using System.Text.RegularExpressions;
using static OpenQA.Selenium.BiDi.Modules.Script.RealmInfo;
using static System.Net.Mime.MediaTypeNames;
using OpenQA.Selenium.Interactions;

namespace TestAutomationFramework.Helpers
{
    public static class WebDriverHelper
    {
        public static Dictionary<string, string> CapturedData;

        static TestStep CurrentStepForCustomeStatus;
        public static IWebDriver InitializeDriver(string browser)
        {
            Logger.Log($"Initializing WebDriver for browser: {browser}", LogLevel.Info);
            IWebDriver driver;

            switch (browser.ToLower())
            {
                case "chrome":
                    Logger.Log("Setting up Chrome WebDriver.", LogLevel.Debug);
                    new DriverManager().SetUpDriver(new ChromeConfig());

                    var options = new ChromeOptions();

                    if (ConfigHelper._RunningInstructions.MobileView)
                    {
                        options.EnableMobileEmulation("iPhone X");
                        Logger.Log("Enabled mobile emulation using 'iPhone X'.", LogLevel.Info);
                    }

                    options.PageLoadStrategy = PageLoadStrategy.Eager;
                    options.AddArgument("--disable-popup-blocking");
                    driver = new ChromeDriver(options);
                    break;

                case "firefox":
                    Logger.Log("Setting up Firefox WebDriver.", LogLevel.Debug);
                    new DriverManager().SetUpDriver(new FirefoxConfig());
                    driver = new FirefoxDriver();
                    break;

                default:
                    Logger.Log($"Browser {browser} is not supported yet.", LogLevel.Error);
                    throw new NotImplementedException($"Browser {browser} is not supported yet.");
            }

            driver.Manage().Window.Maximize();
            Logger.Log("WebDriver initialized and window maximized.", LogLevel.Info);

            driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(Config.Settings.ImplicitWaitInSeconds);
            driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(Config.Settings.PageLoadTimeoutInSeconds);
            driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(Config.Settings.PageLoadTimeoutInSeconds);

            return driver;
        }



        public static ExecutionResult ExecuteTestCase(TestCase testCase)
        {
            CapturedData = new Dictionary<string, string>();

            Stopwatch _ExecutionTimeFromFirstStepInMS;
            Logger.Log($"Executing test case: {testCase.TestCaseName}", LogLevel.Info);
            IWebDriver driver = null;

            // Initialize condition configuration manager
            ConditionConfigurationManager.Initialize();

            // Update test execution state
            ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), "Running", 0);

            ConfigHelper.TestCaseRefrenceToTestResult.TryGetValue(testCase.TestCaseId, out var existingResult);
            existingResult.Name = testCase.TestCaseName;
            existingResult.Status = "In Progress";
            existingResult.TestResultCode = testCase.TestCaseCode;
            existingResult.SuiteName = testCase.SuiteName;
            existingResult.ModuleName = testCase.ModuleName;
            existingResult.OriginalTestCase = testCase;
            existingResult.Environment = testCase.Environment ?? ConfigHelper._RunningInstructions.RunningEnvironment ?? "UAT";

            int ii = 0;
            try
            {
                var browser = testCase.Browser ?? ConfigHelper._RunningInstructions.RunningBrowsers ?? new[] { "chrome" };

                var currentEnv = TestContext.Parameters.Get("ENV")
          ?? (!string.IsNullOrEmpty(testCase.Environment)
              ? testCase.Environment
              : ConfigHelper._RunningInstructions.RunningEnvironment);


                string baseUrl = null;

                if (Config.Settings.BaseUrls != null && Config.Settings.BaseUrls.TryGetValue(currentEnv, out var envBaseUrl))
                {
                    baseUrl = envBaseUrl;
                }
                else
                {
                    baseUrl = testCase.BaseUrl;
                }

                if (string.IsNullOrEmpty(baseUrl))
                {
                    Logger.Log("BaseUrl is not specified in the test case or the parent configuration.", LogLevel.Error);
                    throw new Exception("BaseUrl is not specified in the test case or the parent configuration.");
                }

                Logger.Log($"Using browser: {browser} and BaseUrl: {baseUrl}", LogLevel.Info);
                driver = InitializeDriver(browser[ii]);
                ii++;
                try
                {
                    driver.Navigate().GoToUrl(baseUrl);
                    Logger.Log($"Navigated to BaseUrl: {baseUrl}", LogLevel.Info);
                }
                catch (Exception ex)
                {


                    Logger.Log($"Navigation failed. Error: {ex.Message}", LogLevel.Error);
                    try
                    {
                        Logger.Log($"Attempting loading the page again.", LogLevel.Info);
                        driver.Navigate().GoToUrl(baseUrl);
                        Logger.Log($"Second attempt loading the page Succeeded", LogLevel.Info);
                    }
                    catch (Exception e)
                    {
                        Logger.Log($"Second attempt loading the page failed. Error: {e.Message}", LogLevel.Error);
                        existingResult.ErrorMsg = "Failed to load URL two times.";
                        existingResult.ErrorMsgDetails = $"URL loading error details: {ex.StackTrace}\nRefresh error details:{e.StackTrace}";
                        existingResult.Status = "Failed";
                        throw new Exception("Failed to load URL two times.");
                    }
                }

                _ExecutionTimeFromFirstStepInMS = Stopwatch.StartNew();

                for (int stepIndex = 0; stepIndex < testCase.Steps.Count; stepIndex++)
                {
                    var step = testCase.Steps[stepIndex];
                    var stepResult = new TestStep_Results { Name = step.Name, Result = "Passed" };

                    // Update current step index in test state
                    ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), "Running", stepIndex);

                    if (!string.IsNullOrEmpty(step.CustomStatusToReportUponFailure))
                    {
                        CurrentStepForCustomeStatus = step;
                    }

                    try
                    {
                        // Replace captured data placeholders
                        if (!string.IsNullOrEmpty(step.Value))
                        {
                            step.Value = Regex.Replace(step.Value, @"\{\{CapturedData\.([^\}]+)\}\}", match =>
                            {
                                string key = match.Groups[1].Value;

                                if (CapturedData.TryGetValue(key, out var actualValue))
                                {
                                    return actualValue;
                                }
                                else
                                {
                                    Console.WriteLine($"[Warning] CapturedData does not contain key: '{key}'");
                                    return match.Value;
                                }
                            });
                        }

                        if (!string.IsNullOrEmpty(step.Target) && step.Target.Contains("{{CapturedData."))
                        {
                            step.Target = Regex.Replace(step.Target, @"\{\{CapturedData\.([^\}]+)\}\}", match =>
                            {
                                string key = match.Groups[1].Value;

                                if (CapturedData.TryGetValue(key, out var actualValue))
                                {
                                    return actualValue;
                                }
                                else
                                {
                                    Console.WriteLine($"[Warning] CapturedData does not contain key: '{key}'");
                                    return match.Value;
                                }
                            });
                        }

                        // Handle step conditions with enhanced evaluation
                        if (step.Condition != null)
                        {
                            var conditionResult = EvaluateStepCondition(step, testCase, driver);

                            if (conditionResult.ShouldStop)
                            {
                                _ExecutionTimeFromFirstStepInMS.Stop();
                                testCase.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                                existingResult.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                                existingResult.Status = conditionResult.Status;
                                Logger.Log($"Condition result for step '{step.Name}': {conditionResult.Message}", LogLevel.Info);

                                // Update final test state
                                ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), conditionResult.Status);

                                return new ExecutionResult
                                {
                                    TestName = testCase.TestCaseName,
                                    IsSuccess = conditionResult.IsSuccess
                                };
                            }

                            if (conditionResult.ShouldSkip)
                            {
                                Logger.Log($"Skipping step '{step.Name}': {conditionResult.Message}", LogLevel.Info);
                                stepResult.Result = "Skipped";
                                stepResult.ErrorMsg = conditionResult.Message;
                                existingResult.Steps.Add(stepResult);
                                continue;
                            }
                        }

                        // Execute step with retry logic if configured
                        ExecuteStepWithConditions(driver, step, testCase);
                        // Ensure ExecuteStep handles the step logic


                        WaitForSpinner(driver, step);  // Handle spinner waiting logic
                        if (step.ElementToValidateThatScreenLoaded != null)
                        {
                            try
                            {
                                Thread.Sleep(7000);
                                Logger.Log($"Wait Until find element:{step.ElementToValidateThatScreenLoaded}", LogLevel.Info);

                                By locator = WebDriverHelper.GetByFromTarget(step.ElementToValidateThatScreenLoaded);
                                driver.WaitUntil(ExpectedConditions.ElementIsVisible(locator));
                            }
                            catch
                            {
                                Logger.Log($"Failed To Find Element  :{step.ElementToValidateThatScreenLoaded}", LogLevel.Error);
                            }
                        }


                        stepResult.ScreenshotUrlorPath = driver.CaptureScreenshot(testCase.SuiteName, testCase.ModuleName, testCase.TestCaseName, step.Name);



                        if (!string.IsNullOrEmpty(step.Name))
                        {
                            stepResult.Description = Regex.Replace(step.Name, @"\{\{CapturedData\.([^\}]+)\}\}", match =>
                            {
                                var key = match.Groups[1].Value;

                                if (CapturedData.TryGetValue(key, out var actualValue))
                                {
                                    return actualValue;
                                }
                                else
                                {
                                    Console.WriteLine($"[Warning] CapturedData does not contain key: '{key}' used in step name.");
                                    return match.Value; // Leave placeholder unchanged
                                }
                            });
                        }
                        else
                        {
                            stepResult.Description = step.Name;
                        }


                        if (!string.IsNullOrEmpty(step.NavigateToUrl))
                        {
                            string originalWindow = driver.CurrentWindowHandle;

                            ((IJavaScriptExecutor)driver).ExecuteScript("window.open();");

                            var windowHandles = driver.WindowHandles;

                            driver.SwitchTo().Window(windowHandles.Last());

                            driver.Navigate().GoToUrl(step.NavigateToUrl);
                        }

                    }
                    catch (Exception ex)
                    {


                        Logger.Log($"Error executing step: {step.Command} on target: {step.Target} " +
                            $"{(string.IsNullOrEmpty(step.CustomStatusToReportUponFailure) ? ", CustomStatusToReportUponFailure: " + step.CustomStatusToReportUponFailure : "")}. Details: {ex.Message}", LogLevel.Error);
                        stepResult.Result = "Failed";
                        //existingResult.ErrorMsg = $"Error executing step: {step.Name} on target: {step.Target}.";
                        existingResult.ErrorMsg = $"Error executing step: {step.Name}.";
                        existingResult.ErrorMsgDetails = $"Error Details: {ex.Message}";



                        if (!step.ContinueOnError)
                        {
                            existingResult.Status = "Failed";

                            _ExecutionTimeFromFirstStepInMS.Stop();
                            testCase.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                            existingResult.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                            throw;
                        }
                        else
                        {
                            continue;
                        }
                    }


                    existingResult.Steps.Add(stepResult);

                }
                _ExecutionTimeFromFirstStepInMS.Stop();
                testCase.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                existingResult.ExecutionTimeFromFirstStepInMS = _ExecutionTimeFromFirstStepInMS;
                existingResult.Status = "Passed";

                // Update final test state to completed
                ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), "Completed", testCase.Steps.Count);

                Logger.Log($"Test case executed successfully: {testCase.TestCaseName}", LogLevel.Info);

                return new ExecutionResult
                {
                    TestName = testCase.TestCaseName,
                    IsSuccess = true
                };

            }
            catch (Exception ex)
            {
                existingResult.Status = "Failed";

                // Update test state to failed
                ParallelConditionEvaluator.UpdateTestState(testCase.TestCaseId.ToString(), "Failed");

                Logger.Log($"Error executing test case: {testCase.TestCaseName}. Details: {ex.Message}", LogLevel.Error);

                if (CurrentStepForCustomeStatus != null && !string.IsNullOrEmpty(CurrentStepForCustomeStatus.CustomStatusToReportUponFailure))
                {
                    existingResult.Status = CurrentStepForCustomeStatus.CustomStatusToReportUponFailure;
                    CurrentStepForCustomeStatus = null;
                }
                throw ex;

            }
            finally
            {
                driver?.Quit();
                driver?.Dispose();
                Logger.Log("WebDriver instance quit.", LogLevel.Info);
            }
        }



        /// <summary>
        /// Executes a single step of the test case.
        /// </summary>
        /// <param name="driver">The WebDriver instance.</param>
        /// <param name="step">The test step to execute.</param>
        public static void ExecuteStep(IWebDriver driver, TestStep step)
        {
            Logger.Log($"Executing step: {step.Command} on target: {step.Target}", LogLevel.Debug);
            WaitForSpinner(driver, step);

            Dictionary<string, string> savedText = new Dictionary<string, string>();

            List<string> originalWindowHandles = new List<string>(driver.WindowHandles);


            if (driver.WindowHandles.Count > 1)
            {
                /*                Thread.Sleep(5000);
                */
                var newTabHandle = driver.WindowHandles.LastOrDefault();
                if (newTabHandle != null)
                    driver.SwitchTo().Window(newTabHandle);
                Thread.Sleep(2000);

            }

            // Handle delay before step execution
            var delay = step.CustomDelayBeforeStepExecustionInMilliseconds > 0
                ? step.CustomDelayBeforeStepExecustionInMilliseconds
                : Config.Settings.DelayBeforeStepExecustionInMilliseconds;
            if (delay > 0)
            {
                Logger.Log($"Delaying step execution by {delay} ms.", LogLevel.Debug);
                Thread.Sleep(delay);
            }



            if (step.TargetsToCaptureDataFrom is not null && step.TargetsToCaptureDataFrom.Count != 0)
            {
                foreach (var target in step.TargetsToCaptureDataFrom)
                {

                    string elementText = WebDriverHelper.GetElement(driver, target.Selector)?.Text ?? string.Empty;

                    if (!string.IsNullOrEmpty(target.Regex))
                    {
                        var match = Regex.Match(elementText, target.Regex);
                        CapturedData[target.UniqueKey] = match.Success ? match.Groups[1].Value : string.Empty;
                    }
                    else
                    {
                        CapturedData[target.UniqueKey] = elementText;
                    }
                }
            }



            IWebElement element = null;
            IReadOnlyCollection<IWebElement> elements = null;

            string textboxValue;
            // Check if the command expects a collection
            if (step.Command.ToLower() != "executescript")
            {
                if (step.Command.ToLower() == "countclass")
                {
                    elements = WebDriverHelper.GetElements(driver, step.Target);
                }
                else
                {
                    element = WebDriverHelper.GetElement(driver, step.Target);
                }
            }

            // Execute step based on command
            switch (step.Command.ToLower())
            {
                case "type":
                    Logger.Log($"Typing value: {step.Value} into target: {step.Target}", LogLevel.Debug);
                    element.Clear();
                    element.SendKeys(step.Value);
                    break;

                case "click":
                    try
                    {
                        Logger.Log($"Clicking on target: {step.Target}", LogLevel.Debug);

                        IJavaScriptExecutor js = (IJavaScriptExecutor)driver;
                        js.ExecuteScript("arguments[0].click();", element);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error while clicking on target: {step.Target}. Details: {ex.Message}", LogLevel.Error);
                        if (!step.ContinueOnError) throw;
                    }
                    break;

                case "verify":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    string actualText = element.Text;

                    if (actualText.ToLower() != step.Value.ToLower())
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;
                case "appear":
                    if (!element.Displayed)
                    {
                        Logger.Log($"Element not displayed {step.Target}.", LogLevel.Error);
                        throw new Exception($"Element not displayed {step.Target}");
                    }

                    break;

                case "scrolltoelement":

                    var actions = new Actions(driver);
                    actions.MoveToElement(element).Perform();

                    break;

                case "refute":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    if (element.Text == step.Value)
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;

                case "select":
                    Logger.Log($"Selecting option from dropdown list: {step.Target} with value: {step.Value}", LogLevel.Debug);
                    try
                    {
                        var selectList = new SelectElement(element);
                        selectList.SelectByText(step.Value);
                        Logger.Log($"Option: {step.Value} found and selected in dropdown list: {step.Target}", LogLevel.Debug);
                    }
                    catch
                    {
                        Logger.Log($"Option was not found in dropdown list or other error occurred", LogLevel.Error);
                        throw new Exception($"Option was not found in dropdown list or other error occurred");
                    }


                    break;

                case "lengthequals":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    textboxValue = element.GetAttribute("value");
                    if (textboxValue.Length != step.Value.Length)
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;

                case "lengthnotequals":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    textboxValue = element.GetAttribute("value");
                    if (textboxValue.Length == step.Value.Length)
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;

                case "lengthgreaterthan":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    textboxValue = element.GetAttribute("value");
                    if (textboxValue.Length < step.Value.Length)
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;

                case "lengthlessthan":
                    Logger.Log($"Verifying target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    textboxValue = element.GetAttribute("value");
                    if (textboxValue.Length > step.Value.Length)
                    {
                        Logger.Log($"Verification failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Verification failed for {step.Target}");
                    }
                    break;

                case "executescript":
                    Logger.Log($"Executing script: {step.Value}", LogLevel.Debug);
                    var jsExecutor = (IJavaScriptExecutor)driver;
                    jsExecutor.ExecuteScript(step.Value);
                    break;

                case "waitforelement":
                    Logger.Log($"Waiting for element: {step.Target} to be invisible for {step.Value} ms.", LogLevel.Debug);
                    driver.WaitUntil(ExpectedConditions.InvisibilityOfElementLocated(WebDriverHelper.GetByFromTarget(step.Target)), int.Parse(step.Value));
                    break;

                case "assert":
                    Logger.Log($"Asserting target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    if (!element.Text.Contains(step.Value))
                    {
                        Logger.Log($"Assertion failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Assertion failed for {step.Target}");
                    }
                    break;
                case "assertvisual":
                    Logger.Log($"Asserting target: {step.Target} with expected value: {step.Value}", LogLevel.Debug);
                    string targetElement = GetElement(driver, step.Target).GetAttribute("class").Split(' ', StringSplitOptions.RemoveEmptyEntries).Last();

                    if (step.Value == "system")
                    {
                        var jsExecutorSys = (IJavaScriptExecutor)driver;
                        var systemTheme = jsExecutorSys.ExecuteScript("return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';");
                        if (systemTheme.ToString().ToLower() != targetElement.ToLower())
                        {
                            Logger.Log($"Assertion failed for {step.Target}.", LogLevel.Error);
                            throw new Exception($"Assertion failed for {step.Target}");
                        }
                    }
                    else if (step.Value.ToLower() != targetElement.ToLower())
                    {
                        Logger.Log($"Assertion failed for {step.Target}.", LogLevel.Error);
                        throw new Exception($"Assertion failed for {step.Target}");
                    }
                    break;

                case "countclass":
                    Logger.Log($"Counting children of target class: {step.Target}", LogLevel.Debug);

                    int childCount = elements.Count;

                    Logger.Log($"Found {childCount} children under element with class: {step.Target}", LogLevel.Debug);

                    if (!int.TryParse(step.Value, out int expectedCount))
                    {
                        Logger.Log($"Invalid expected child count: {step.Value}", LogLevel.Error);
                        throw new Exception($"Invalid expected child count: {step.Value}");
                    }

                    if (childCount != expectedCount)
                    {
                        Logger.Log($"Expected {expectedCount} children but found {childCount} under class: {step.Target}", LogLevel.Error);
                        throw new Exception($"Mismatch in child count for class {step.Target}: expected {expectedCount}, found {childCount}");
                    }
                    break;



                default:
                    Logger.Log($"Step type {step.Command} is not supported.", LogLevel.Error);
                    throw new NotImplementedException($"Step type {step.Command} is not supported.");
            }
        }
        public static void WaitForSpinner(IWebDriver driver, TestStep step)
        {
            if (!step.SkipWaitingSpinner)
            {
                Logger.Log("Waiting for loading spinner to disappear.", LogLevel.Debug);
                driver.WaitUntil(
                    ExpectedConditions.InvisibilityOfElementLocated(
                        WebDriverHelper.GetByFromTarget(Config.Settings.LoadingSpinnerTargetSelector)),
                    step.CustomWaitTimeForSpinnerInMilliseconds <= 0
                        ? Config.Settings.GeneralWaitTimeForSpinnerInMilliseconds
                        : step.CustomWaitTimeForSpinnerInMilliseconds);
            }
        }
        public static IWebElement GetElement(IWebDriver driver, string target)
        {
            try
            {
                Logger.Log($"Locating element with target: {target}", LogLevel.Debug);
                var by = GetByFromTarget(target);
                return driver.FindElement(by);
            }
            catch (Exception ex)
            {
                throw new Exception($"Can`t Find Element with target: {target} ");
            }

        }
        public static IReadOnlyCollection<IWebElement> GetElements(IWebDriver driver, string target)
        {
            try
            {
                Logger.Log($"Locating element with target: {target}", LogLevel.Debug);
                var by = GetByFromTarget(target);
                return driver.FindElements(by);
            }
            catch (Exception ex)
            {
                throw new Exception($"Can`t Find Element with target: {target} ");
            }

        }

        public static By GetByFromTarget(string target)
        {
            Logger.Log($"Parsing selector: {target}", LogLevel.Debug);
            if (string.IsNullOrWhiteSpace(target))
                throw new ArgumentException("Target selector cannot be null or empty.");

            if (target.StartsWith("id=", StringComparison.OrdinalIgnoreCase))
                return By.Id(target.Substring(3));
            else if (target.StartsWith("css=", StringComparison.OrdinalIgnoreCase))
                return By.CssSelector(target.Substring(4));
            else if (target.StartsWith("xpath=", StringComparison.OrdinalIgnoreCase))
                return By.XPath(target.Substring(6));
            else if (target.StartsWith("name=", StringComparison.OrdinalIgnoreCase))
                return By.Name(target.Substring(5));
            else if (target.StartsWith("tag=", StringComparison.OrdinalIgnoreCase))
                return By.TagName(target.Substring(4));
            else if (target.StartsWith("link=", StringComparison.OrdinalIgnoreCase))
                return By.LinkText(target.Substring(5));
            else if (target.StartsWith("partialLink=", StringComparison.OrdinalIgnoreCase))
                return By.PartialLinkText(target.Substring(12));
            else if (target.StartsWith("class=", StringComparison.OrdinalIgnoreCase))
                return By.ClassName(target.Substring(6));
            else
                throw new InvalidOperationException($"Unsupported selector type: {target}");
        }

        /// <summary>
        /// Evaluate step condition with enhanced condition types
        /// </summary>
        private static StepConditionResult EvaluateStepCondition(TestStep step, TestCase testCase, IWebDriver driver)
        {
            try
            {
                // Build comprehensive context using ContextManager
                var context = ContextManager.BuildContext(testCase, step);

                // Evaluate condition with detailed result
                var conditionResult = ConditionEvaluator.EvaluateWithResult(step.Condition, context);

                // Handle different condition types
                if (step.Condition.Skip != null)
                {
                    if (conditionResult.IsSuccess)
                    {
                        return new StepConditionResult
                        {
                            ShouldSkip = true,
                            Message = step.Condition.Skip.Reason ?? "Step skipped due to condition",
                            IsSuccess = true
                        };
                    }
                }

                if (step.Condition.HoldUntil != null)
                {
                    if (!conditionResult.IsSuccess)
                    {
                        return new StepConditionResult
                        {
                            ShouldStop = true,
                            Status = "Failed",
                            Message = $"Hold condition not met: {conditionResult.Message}",
                            IsSuccess = false
                        };
                    }
                }

                // Handle traditional stop conditions
                if (!string.IsNullOrEmpty(step.Condition.TestCaseStatus) && conditionResult.IsSuccess)
                {
                    return new StepConditionResult
                    {
                        ShouldStop = true,
                        Status = step.Condition.TestCaseStatus,
                        Message = $"Stop condition met: {conditionResult.Message}",
                        IsSuccess = true
                    };
                }

                return new StepConditionResult
                {
                    ShouldContinue = true,
                    Message = conditionResult.Message,
                    IsSuccess = conditionResult.IsSuccess
                };
            }
            catch (Exception ex)
            {
                Logger.Log($"Error evaluating step condition: {ex.Message}", LogLevel.Error);
                return new StepConditionResult
                {
                    ShouldStop = true,
                    Status = "Failed",
                    Message = $"Condition evaluation failed: {ex.Message}",
                    IsSuccess = false
                };
            }
        }

        /// <summary>
        /// Execute step with enhanced condition support (retry, resource locking, etc.)
        /// </summary>
        private static void ExecuteStepWithConditions(IWebDriver driver, TestStep step, TestCase testCase)
        {
            try
            {
                // Handle resource locking
                if (step.Command.Equals("lockResource", StringComparison.OrdinalIgnoreCase))
                {
                    var resourceName = step.Target;
                    var lockDuration = TimeSpan.FromMilliseconds(int.Parse(step.Value ?? "300000"));
                    var lockResult = ParallelConditionEvaluator.LockResource(resourceName, lockDuration, testCase.TestCaseId.ToString(), $"Locked by step: {step.Name}");

                    if (!lockResult.IsSuccess)
                    {
                        throw new InvalidOperationException($"Failed to lock resource '{resourceName}': {lockResult.Message}");
                    }

                    Logger.Log($"Successfully locked resource: {resourceName}", LogLevel.Info);
                    return;
                }

                // Handle resource release
                if (step.Command.Equals("releaseResource", StringComparison.OrdinalIgnoreCase))
                {
                    var resourceName = step.Target;
                    var releaseResult = ParallelConditionEvaluator.ReleaseResource(resourceName, testCase.TestCaseId.ToString());

                    if (!releaseResult.IsSuccess)
                    {
                        Logger.Log($"Warning: Failed to release resource '{resourceName}': {releaseResult.Message}", LogLevel.Warn);
                    }
                    else
                    {
                        Logger.Log($"Successfully released resource: {resourceName}", LogLevel.Info);
                    }
                    return;
                }

                // Handle retry conditions
                if (step.Condition?.Retry != null)
                {
                    var retryResult = RetryConditionEvaluator.ExecuteStepWithRetry(step, driver, step.Condition.Retry);

                    if (!retryResult.IsSuccess)
                    {
                        throw new InvalidOperationException($"Step failed after retry: {retryResult.Message}");
                    }

                    Logger.Log($"Step executed successfully with retry: {step.Name}", LogLevel.Info);
                    return;
                }

                // Execute step normally
                ExecuteStep(driver, step);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error executing step with conditions '{step.Name}': {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Result of step condition evaluation
        /// </summary>
        private class StepConditionResult
        {
            public bool ShouldStop { get; set; }
            public bool ShouldSkip { get; set; }
            public bool ShouldContinue { get; set; }
            public string Status { get; set; } = "Passed";
            public string Message { get; set; } = "";
            public bool IsSuccess { get; set; } = true;
        }
    }
}
