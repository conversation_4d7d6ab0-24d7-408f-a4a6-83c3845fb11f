namespace TestAutomationFramework.Models
{
    /// <summary>
    /// Enumeration of condition types for better type safety
    /// </summary>
    public enum ConditionType
    {
        Predicate,
        Reference,
        HoldUntil,
        Retry,
        Skip,
        Environment,
        FeatureFlag,
        AnyOf,
        AllOf,
        Not
    }

    /// <summary>
    /// Condition execution result
    /// </summary>
    public class ConditionResult
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public Dictionary<string, object>? Data { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public Exception? Exception { get; set; }

        public static ConditionResult Success(string? message = null, Dictionary<string, object>? data = null)
        {
            return new ConditionResult
            {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        public static ConditionResult Failure(string message, Exception? exception = null)
        {
            return new ConditionResult
            {
                IsSuccess = false,
                Message = message,
                Exception = exception
            };
        }
    }

    /// <summary>
    /// Resource lock configuration
    /// </summary>
    public class ResourceLockConfig
    {
        public string ResourceName { get; set; } = string.Empty;
        public TimeSpan LockTimeout { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan AcquisitionTimeout { get; set; } = TimeSpan.FromMinutes(10);
        public bool AutoRelease { get; set; } = true;
        public string? LockReason { get; set; }
    }

    /// <summary>
    /// Test prerequisite configuration
    /// </summary>
    public class TestPrerequisite
    {
        public string TestCaseId { get; set; } = string.Empty;
        public string? TestCaseName { get; set; }
        public string ExpectedStatus { get; set; } = "Completed";
        public TimeSpan MaxWaitTime { get; set; } = TimeSpan.FromMinutes(30);
        public bool FailOnTimeout { get; set; } = true;
    }

    /// <summary>
    /// Feature flag configuration
    /// </summary>
    public class FeatureFlagConfig
    {
        public string Name { get; set; } = string.Empty;
        public bool DefaultValue { get; set; } = false;
        public Dictionary<string, bool> EnvironmentOverrides { get; set; } = new();
        public DateTime? ExpirationDate { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// Environment configuration
    /// </summary>
    public class EnvironmentConfig
    {
        public string Name { get; set; } = string.Empty;
        public Dictionary<string, object> Settings { get; set; } = new();
        public List<FeatureFlagConfig> FeatureFlags { get; set; } = new();
        public Dictionary<string, string> ConnectionStrings { get; set; } = new();
        public Dictionary<string, string> ApiEndpoints { get; set; } = new();
    }

    /// <summary>
    /// Retry policy configuration
    /// </summary>
    public class RetryPolicy
    {
        public int MaxAttempts { get; set; } = 3;
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
        public double BackoffMultiplier { get; set; } = 2.0;
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromMinutes(1);
        public List<Type> RetryableExceptions { get; set; } = new();
        public Func<Exception, bool>? ShouldRetry { get; set; }
    }

    /// <summary>
    /// External system health check configuration
    /// </summary>
    public class ExternalSystemCheck
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
        public Dictionary<string, string> Headers { get; set; } = new();
        public string? ExpectedResponse { get; set; }
        public int ExpectedStatusCode { get; set; } = 200;
    }

    /// <summary>
    /// Condition evaluation context
    /// </summary>
    public class ConditionEvaluationContext
    {
        public TestCase TestCase { get; set; } = new();
        public TestStep? CurrentStep { get; set; }
        public IDictionary<string, object> RuntimeContext { get; set; } = new Dictionary<string, object>();
        public DateTime EvaluationTime { get; set; } = DateTime.Now;
        public string Environment { get; set; } = "UAT";
        public Dictionary<string, object> CustomData { get; set; } = new();
    }

    /// <summary>
    /// Condition configuration for reusable conditions
    /// </summary>
    public class ConditionConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Condition Condition { get; set; } = new();
        public List<string> ApplicableEnvironments { get; set; } = new();
        public Dictionary<string, string> DefaultParameters { get; set; } = new();
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastModified { get; set; }
    }

    /// <summary>
    /// Parallel execution coordinator
    /// </summary>
    public class ParallelExecutionCoordinator
    {
        private static readonly Dictionary<string, ManualResetEventSlim> _testCompletionEvents = new();
        private static readonly Dictionary<string, string> _testStatuses = new();
        private static readonly object _lock = new();

        public static void RegisterTestCompletion(string testCaseId, string status)
        {
            lock (_lock)
            {
                _testStatuses[testCaseId] = status;
                
                if (_testCompletionEvents.TryGetValue(testCaseId, out var eventSlim))
                {
                    eventSlim.Set();
                }
            }
        }

        public static bool WaitForTestCompletion(string testCaseId, TimeSpan timeout, string expectedStatus = "Completed")
        {
            lock (_lock)
            {
                // Check if test is already completed with expected status
                if (_testStatuses.TryGetValue(testCaseId, out var currentStatus) && 
                    currentStatus.Equals(expectedStatus, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                // Create or get existing event
                if (!_testCompletionEvents.TryGetValue(testCaseId, out var eventSlim))
                {
                    eventSlim = new ManualResetEventSlim(false);
                    _testCompletionEvents[testCaseId] = eventSlim;
                }
            }

            // Wait for completion
            var completed = eventSlim.Wait(timeout);
            
            if (completed)
            {
                lock (_lock)
                {
                    return _testStatuses.TryGetValue(testCaseId, out var finalStatus) && 
                           finalStatus.Equals(expectedStatus, StringComparison.OrdinalIgnoreCase);
                }
            }

            return false;
        }

        public static void Cleanup()
        {
            lock (_lock)
            {
                foreach (var eventSlim in _testCompletionEvents.Values)
                {
                    eventSlim.Dispose();
                }
                _testCompletionEvents.Clear();
                _testStatuses.Clear();
            }
        }
    }
}
