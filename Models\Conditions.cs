
namespace TestAutomationFramework.Models
{
    // Root condition wrapper (can hold different types)
    public class Condition
    {
        public List<Condition>? AnyOf { get; set; }

        public List<Condition>? AllOf { get; set; }

        public Condition? Not { get; set; }

        public PredicateCondition? Predicate { get; set; }

        public ConditionReference? Reference { get; set; }

        public string? TestCaseStatus { get; set; }

        // New condition types
        public HoldUntilCondition? HoldUntil { get; set; }

        public RetryCondition? Retry { get; set; }

        public SkipCondition? Skip { get; set; }

        public EnvironmentCondition? Environment { get; set; }

        public FeatureFlagCondition? FeatureFlag { get; set; }
    }

    // Predicate condition (context/field/operator/value)
    public class PredicateCondition
    {
        public string Context { get; set; } = string.Empty;

        public string? Field { get; set; }

        public string Operator { get; set; } = string.Empty;

        public string? Value { get; set; }
    }

    // Reference to external condition file
    public class ConditionReference
    {
        /// <summary>
        /// Path to condition JSON file (relative or absolute)
        /// </summary>
        public string Ref { get; set; } = string.Empty;

        /// <summary>
        /// Parameters passed from step (key -> value)
        /// </summary>
        public Dictionary<string, string>? Params { get; set; }

    }

    // Hold until condition for parallel execution coordination
    public class HoldUntilCondition
    {
        /// <summary>
        /// Test case name or ID to wait for completion
        /// </summary>
        public string? TestCaseName { get; set; }

        /// <summary>
        /// Test case ID to wait for completion
        /// </summary>
        public string? TestCaseId { get; set; }

        /// <summary>
        /// Resource name to wait for availability
        /// </summary>
        public string? ResourceName { get; set; }

        /// <summary>
        /// Maximum wait time in milliseconds
        /// </summary>
        public int MaxWaitTimeMs { get; set; } = 300000; // 5 minutes default

        /// <summary>
        /// Polling interval in milliseconds
        /// </summary>
        public int PollingIntervalMs { get; set; } = 1000; // 1 second default

        /// <summary>
        /// Expected status of the test case to wait for
        /// </summary>
        public string ExpectedStatus { get; set; } = "Completed";
    }

    // Retry condition for handling failures
    public class RetryCondition
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Delay between retries in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Exponential backoff multiplier
        /// </summary>
        public double BackoffMultiplier { get; set; } = 1.5;

        /// <summary>
        /// Exception types that should trigger retry
        /// </summary>
        public List<string>? RetryOnExceptions { get; set; }

        /// <summary>
        /// Condition that must be met to retry
        /// </summary>
        public Condition? RetryWhen { get; set; }
    }

    // Skip condition for conditional test execution
    public class SkipCondition
    {
        /// <summary>
        /// Reason for skipping
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Condition that determines if test should be skipped
        /// </summary>
        public Condition? When { get; set; }
    }

    // Environment-based condition
    public class EnvironmentCondition
    {
        /// <summary>
        /// Target environment name
        /// </summary>
        public string? TargetEnvironment { get; set; }

        /// <summary>
        /// List of environments where condition applies
        /// </summary>
        public List<string>? Environments { get; set; }

        /// <summary>
        /// Behavior configuration per environment
        /// </summary>
        public Dictionary<string, object>? EnvironmentConfig { get; set; }
    }

    // Feature flag condition
    public class FeatureFlagCondition
    {
        /// <summary>
        /// Feature flag name
        /// </summary>
        public string FeatureName { get; set; } = string.Empty;

        /// <summary>
        /// Expected feature flag value
        /// </summary>
        public bool ExpectedValue { get; set; } = true;

        /// <summary>
        /// Environment-specific feature flag overrides
        /// </summary>
        public Dictionary<string, bool>? EnvironmentOverrides { get; set; }
    }
}
