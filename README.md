# Test Automation Framework

A comprehensive test automation framework built with C# and .NET 8.0 for web application testing, specifically designed for banking/financial applications.

## 🚀 Overview

This framework provides a robust foundation for automated testing with support for:
- Web UI testing using Selenium WebDriver
- Data-driven testing with JSON configuration
- Modular test organization
- Comprehensive reporting and logging
- Email notifications
- Multi-environment support

## 🛠️ Technology Stack

- **.NET 8.0** - Target framework
- **NUnit 4.3.2** - Testing framework
- **Selenium WebDriver 4.28.0** - Web automation
- **PuppeteerSharp 20.2.2** - Additional browser automation
- **Newtonsoft.Json 13.0.3** - JSON handling
- **Microsoft.Extensions.Configuration** - Configuration management

## 📁 Project Structure

```
TestAutomationFramework/
├── Attributes/              # Custom test attributes
│   └── CustomRetryAttribute.cs
├── BasePath/               # Base directories for test data and reports
│   ├── FoldersAndFilesStructureSample/
│   ├── GeneratedReports/
│   └── TestData/
├── Exceptions/             # Custom exceptions
│   └── CustomeBlockedException.cs
├── Extensions/             # Extension methods
│   └── WebDriverExtensions.cs
├── Helpers/               # Utility classes
│   ├── ConditionEvaluator.cs
│   ├── Config.cs
│   ├── ConfigHelper.cs
│   ├── EmailSender.cs
│   ├── Logger.cs
│   ├── ReportGenerator.cs
│   ├── TestSuiteExecutor.cs
│   └── WebDriverHelper.cs
├── Models/                # Data models
│   ├── AppSettings.cs
│   ├── TestCase.cs
│   ├── TestSuite.cs
│   ├── ExecutionResult.cs
│   └── [other models]
├── Projects/              # Test projects (CA - Credit Agricole Egypt)
│   └── CA/
│       ├── WorkSet01-04/  # Different work sets
│       └── MainWorkSet/   # Main test suite
├── Scripts/               # PowerShell utility scripts
├── TestRunner/            # Test execution engine
│   └── TestRunner.cs
├── EngineStartup.cs       # Framework initialization
├── appsettings.json       # Configuration file
└── NUnit.runsettings     # NUnit configuration
```

## ⚙️ Configuration

### appsettings.json
The main configuration file contains:

```json
{
  "WorkDirectories": ["..\\..\\..\\Projects\\CA"],
  "TestSuiteFolderPaths": [
    "WorkSet01\\TestSuites",
    "WorkSet02\\TestSuites", 
    "WorkSet03\\TestSuites",
    "WorkSet04\\TestSuites",
    "MainWorkSet\\TestSuites"
  ],
  "BaseUrls": {
    "UAT": "https://digitalbanking.ebseg.com/...",
    "Pilot": "https://ebs.ca-egypt.com/...",
    "UAT61": "https://banki-uat61.ebseg.com/..."
  },
  "MailSettings": {
    "smtpServer": "mail.ebseg.com",
    "smtpPort": 587,
    "recipients": ["<EMAIL>", "<EMAIL>"]
  }
}
```

## 🏃‍♂️ Getting Started

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Chrome browser (for ChromeDriver)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Build the project:
   ```bash
   dotnet build
   ```

### Running Tests

#### Command Line
```bash
# Run all tests
dotnet test

# Run specific test suite
dotnet test --filter "Category=Sprint1"

# Run with custom parameters
dotnet test -- TestRunParameters.Parameter(name="Projects", value="path/to/project")
```

#### Visual Studio
1. Open Test Explorer
2. Build the solution
3. Run tests from Test Explorer

## 📝 Test Organization

### Test Structure
Tests are organized in a hierarchical structure:
- **WorkSets**: Different versions or phases of testing
- **Modules**: Functional areas (Login, Transfers, Beneficiaries, etc.)
- **TestSuites**: Collections of related test cases
- **TestCases**: Individual test scenarios

### Test Case Format
Test cases are defined in JSON format with the following structure:
- Test case metadata (name, code, description)
- Test steps with actions and expected results
- Test data and parameters
- Module dependencies

## 🔧 Key Features

### 1. Modular Design
- Reusable modules for common functionality
- Page Object Model implementation
- Separation of test logic and test data

### 2. Data-Driven Testing
- JSON-based test data management
- Parameter substitution
- Environment-specific configurations

### 3. Robust Reporting
- Detailed test execution reports
- Screenshot capture on failures
- Email notifications with results

### 4. Error Handling
- Custom retry mechanisms
- Comprehensive logging
- Graceful failure handling

### 5. Multi-Environment Support
- UAT, Pilot, and Production environments
- Environment-specific URLs and configurations
- Easy environment switching

## 📊 Logging and Reporting

The framework provides comprehensive logging through the `Logger` class:
- Different log levels (Info, Warn, Error)
- File-based logging
- Console output
- Integration with test reports

Reports are generated in the `Results` directory and include:
- Test execution summary
- Detailed step-by-step results
- Screenshots for failed tests
- Performance metrics

## 🔍 Troubleshooting

### Common Issues
1. **WebDriver Issues**: Ensure ChromeDriver is compatible with your Chrome version
2. **Configuration Errors**: Verify paths in appsettings.json
3. **Test Data**: Check JSON format and file paths
4. **Environment Access**: Verify network connectivity to test environments

### Debug Mode
Enable detailed logging by setting log level to Debug in the configuration.

## 🤝 Contributing

1. Follow the existing code structure and naming conventions
2. Add appropriate unit tests for new functionality
3. Update documentation for any new features
4. Ensure all tests pass before submitting changes

## 📄 License

[Add your license information here]

## 📞 Support

For questions or issues, contact the development team or create an issue in the repository.
