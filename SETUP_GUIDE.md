# Test Automation Framework Setup Guide

## 🚀 Quick Start

This guide will help you set up and run the Test Automation Framework on your local machine.

## 📋 Prerequisites

### Required Software
- **.NET 8.0 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/8.0)
- **Visual Studio 2022** (recommended) or **VS Code**
- **Git** for version control
- **Google Chrome** (latest version)

### Optional Tools
- **NUnit Console Runner** for command-line execution
- **Allure** for advanced reporting (if needed)

## 🛠️ Installation Steps

### 1. Clone the Repository
```bash
git clone <repository-url>
cd TestAutomationFramework
```

### 2. Verify .NET Installation
```bash
dotnet --version
# Should show 8.0.x or higher
```

### 3. Restore Dependencies
```bash
dotnet restore
```

### 4. Build the Project
```bash
dotnet build
```

### 5. Verify Installation
```bash
dotnet test --list-tests
# Should display available test cases
```

## ⚙️ Configuration Setup

### 1. Environment Configuration

#### appsettings.json
Update the configuration file with your environment-specific settings:

```json
{
  "WorkDirectories": ["..\\..\\..\\Projects\\CA"],
  "BaseUrls": {
    "UAT": "https://your-uat-environment.com",
    "Pilot": "https://your-pilot-environment.com"
  },
  "MailSettings": {
    "smtpServer": "your-smtp-server.com",
    "smtpPort": 587,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "your-password",
    "recipients": ["<EMAIL>", "<EMAIL>"]
  }
}
```

#### NUnit.runsettings
Configure test execution parameters:

```xml
<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <TestRunParameters>
    <Parameter name="Projects" value="..\\..\\..\\Projects\\CA" />
    <Parameter name="SuitePaths" value="WorkSet03\\TestSuites" />
  </TestRunParameters>
</RunSettings>
```

### 2. Test Data Setup

#### Directory Structure
Ensure your test project follows this structure:
```
Projects/
└── CA/
    ├── WorkSet01/
    │   ├── TestSuites/
    │   ├── TestCases/
    │   ├── Modules/
    │   └── TestData/
    ├── WorkSet02/
    ├── WorkSet03/
    └── MainWorkSet/
```

#### Test Data Files
Create JSON files for test data in the appropriate directories:
- `GlobalParams.json` - Global parameters
- `ExcludedTestData.json` - Excluded test cases
- Module-specific test data files

### 3. Browser Setup

#### ChromeDriver
The framework uses WebDriverManager to automatically manage ChromeDriver. No manual setup required.

#### Alternative Browsers
To use other browsers, update the WebDriverHelper configuration:
```csharp
// In WebDriverHelper.cs
public static IWebDriver CreateDriver(string browserType = "chrome")
{
    switch (browserType.ToLower())
    {
        case "firefox":
            return new FirefoxDriver();
        case "edge":
            return new EdgeDriver();
        default:
            return new ChromeDriver();
    }
}
```

## 🏃‍♂️ Running Tests

### Command Line Execution

#### Run All Tests
```bash
dotnet test
```

#### Run Specific Test Suite
```bash
dotnet test --filter "Category=Sprint1"
```

#### Run with Custom Parameters
```bash
dotnet test -- TestRunParameters.Parameter(name="Projects", value="path\\to\\project")
```

#### Run with Specific Suite Paths
```bash
dotnet test -- TestRunParameters.Parameter(name="SuitePaths", value="WorkSet03\\TestSuites#WorkSet04\\TestSuites")
```

### Visual Studio Execution

1. **Open Solution**: Load `TestAutomationFramework.sln`
2. **Build Solution**: Build → Build Solution (Ctrl+Shift+B)
3. **Open Test Explorer**: Test → Test Explorer
4. **Run Tests**: Select tests and click Run

### VS Code Execution

1. **Install Extensions**:
   - C# for Visual Studio Code
   - .NET Core Test Explorer

2. **Open Project**: File → Open Folder
3. **Run Tests**: Use Test Explorer or terminal

## 🔧 IDE Configuration

### Visual Studio 2022

#### Recommended Extensions
- NUnit 3 Test Adapter
- Selenium WebDriver Support
- JSON Editor

#### Project Settings
1. Set startup project to TestAutomationFramework
2. Configure debugging settings
3. Set up code analysis rules

### VS Code

#### Required Extensions
```json
{
  "recommendations": [
    "ms-dotnettools.csharp",
    "formulahendry.dotnet-test-explorer",
    "ms-vscode.vscode-json"
  ]
}
```

#### Settings Configuration
```json
{
  "dotnet-test-explorer.testProjectPath": "**/*Tests.csproj",
  "dotnet-test-explorer.useTreeView": true,
  "dotnet-test-explorer.showCodeLens": true
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Build Errors
**Problem**: Package restore failures
**Solution**:
```bash
dotnet clean
dotnet restore --force
dotnet build
```

#### 2. Test Discovery Issues
**Problem**: Tests not appearing in Test Explorer
**Solution**:
- Verify project builds successfully
- Check NUnit.runsettings configuration
- Rebuild solution

#### 3. WebDriver Issues
**Problem**: ChromeDriver compatibility errors
**Solution**:
- Update Chrome browser to latest version
- Clear WebDriverManager cache
- Manually specify ChromeDriver version

#### 4. Configuration Errors
**Problem**: Invalid paths in appsettings.json
**Solution**:
- Use absolute paths for testing
- Verify directory structure exists
- Check file permissions

#### 5. Test Data Loading
**Problem**: JSON parsing errors
**Solution**:
- Validate JSON syntax
- Check file encoding (UTF-8)
- Verify file paths are correct

### Debug Mode

#### Enable Detailed Logging
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

#### Debugging Tests
1. Set breakpoints in test code
2. Run tests in debug mode (F5)
3. Use immediate window for inspection

## 📊 Verification

### Test Execution Verification
```bash
# Run a simple test to verify setup
dotnet test --filter "TestMethod=SampleTest" --verbosity normal
```

### Output Verification
Check these directories for generated files:
- `Results/` - Test execution results
- `GeneratedReports/` - HTML reports
- `ScreenShots/` - Failure screenshots
- `TextLogFiles/` - Detailed logs

### Performance Verification
Monitor these metrics:
- Test execution time
- Memory usage
- Browser startup time
- Report generation time

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: Test Automation

on: [push, pull_request]

jobs:
  test:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
    - name: Restore dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --no-restore
    - name: Test
      run: dotnet test --no-build --verbosity normal
```

### Azure DevOps Pipeline
```yaml
trigger:
- main

pool:
  vmImage: 'windows-latest'

steps:
- task: UseDotNet@2
  inputs:
    version: '8.0.x'
- task: DotNetCoreCLI@2
  inputs:
    command: 'restore'
- task: DotNetCoreCLI@2
  inputs:
    command: 'build'
- task: DotNetCoreCLI@2
  inputs:
    command: 'test'
    arguments: '--logger trx --collect "Code coverage"'
```

## 📞 Support

If you encounter issues during setup:
1. Check the troubleshooting section above
2. Review the logs in the output directories
3. Contact the development team
4. Create an issue in the repository

## 🎯 Next Steps

After successful setup:
1. Review the [Architecture Guide](ARCHITECTURE.md)
2. Read the [User Guide](USER_GUIDE.md)
3. Explore sample test cases
4. Create your first test module
