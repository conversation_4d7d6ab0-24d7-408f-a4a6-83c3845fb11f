# Test Automation Framework User Guide

## 📖 Overview

This guide provides detailed instructions for using the Test Automation Framework to create, execute, and manage automated tests.

## 🎯 Getting Started

### Understanding the Framework Structure

The framework is organized around these key concepts:
- **WorkSets**: Different versions or phases of your application
- **Modules**: Functional areas (Login, Transfers, etc.)
- **Test Suites**: Collections of related test cases
- **Test Cases**: Individual test scenarios

## 📝 Creating Test Cases

### 1. Test Case Structure

Test cases are defined in JSON format with this structure:

```json
{
  "TestCaseName": "Login with Valid Credentials",
  "TestCaseCode": "TC001",
  "Description": "Verify user can login with valid username and password",
  "Module": "Login",
  "Priority": "High",
  "TestSteps": [
    {
      "StepNumber": 1,
      "Action": "Navigate to login page",
      "ExpectedResult": "Login page is displayed"
    },
    {
      "StepNumber": 2,
      "Action": "Enter valid username",
      "ExpectedResult": "Username is entered successfully"
    }
  ],
  "TestData": {
    "username": "<EMAIL>",
    "password": "TestPassword123"
  }
}
```

### 2. Creating a New Test Case

#### Step 1: Choose the Right Location
Place your test case JSON file in the appropriate directory:
```
Projects/CA/WorkSet03/TestCases/[ModuleName]/[TestCaseName].json
```

#### Step 2: Define Test Case Metadata
```json
{
  "TestCaseName": "Your Test Case Name",
  "TestCaseCode": "TC_XXX",
  "Description": "Brief description of what this test validates",
  "Module": "ModuleName",
  "Priority": "High|Medium|Low",
  "Tags": ["smoke", "regression", "sanity"]
}
```

#### Step 3: Add Test Steps
```json
{
  "TestSteps": [
    {
      "StepNumber": 1,
      "Action": "Describe the action to perform",
      "ExpectedResult": "Describe the expected outcome",
      "TestData": {
        "field1": "value1",
        "field2": "value2"
      }
    }
  ]
}
```

### 3. Test Data Management

#### Global Parameters
Create `GlobalParams.json` for shared data:
```json
{
  "BaseURL": "https://your-application.com",
  "DefaultTimeout": 30,
  "BrowserType": "Chrome",
  "Environment": "UAT"
}
```

#### Module-Specific Data
Create data files for each module:
```json
{
  "LoginData": {
    "ValidUser": {
      "username": "<EMAIL>",
      "password": "ValidPass123"
    },
    "InvalidUser": {
      "username": "<EMAIL>",
      "password": "WrongPass"
    }
  }
}
```

## 🏃‍♂️ Running Tests

### 1. Command Line Execution

#### Basic Commands
```bash
# Run all tests
dotnet test

# Run tests with verbose output
dotnet test --verbosity detailed

# Run specific category
dotnet test --filter "Category=Smoke"

# Run specific test
dotnet test --filter "FullyQualifiedName~LoginTest"
```

#### Advanced Filtering
```bash
# Run tests by priority
dotnet test --filter "Priority=High"

# Run tests by module
dotnet test --filter "Module=Login"

# Combine filters
dotnet test --filter "Category=Smoke&Priority=High"
```

### 2. IDE Execution

#### Visual Studio
1. Open Test Explorer (Test → Test Explorer)
2. Build the solution
3. Select tests to run
4. Click "Run" or "Debug"

#### VS Code
1. Install .NET Core Test Explorer extension
2. Open Test Explorer panel
3. Select and run tests

### 3. Parameterized Execution

#### Custom Work Directory
```bash
dotnet test -- TestRunParameters.Parameter(name="Projects", value="C:\\MyTests\\Projects\\CA")
```

#### Specific Test Suites
```bash
dotnet test -- TestRunParameters.Parameter(name="SuitePaths", value="WorkSet03\\TestSuites\\Sprint1")
```

#### Multiple Suite Paths
```bash
dotnet test -- TestRunParameters.Parameter(name="SuitePaths", value="WorkSet03\\TestSuites#WorkSet04\\TestSuites")
```

## 📊 Understanding Results

### 1. Console Output
The framework provides real-time feedback:
```
[INFO] TestRunner started.......
[INFO] Total test cases found: 25
[INFO] Starting test execution...
[PASS] TC001 - Login with Valid Credentials
[FAIL] TC002 - Login with Invalid Credentials
[INFO] Test execution completed
```

### 2. Generated Reports

#### HTML Reports
Located in `Results/GeneratedReports/`:
- Test execution summary
- Detailed step results
- Screenshots for failures
- Performance metrics

#### Log Files
Located in `Results/TextLogFiles/`:
- Detailed execution logs
- Error stack traces
- Debug information

### 3. Email Notifications
Automatic email reports include:
- Test execution summary
- Pass/fail statistics
- Links to detailed reports
- Failure analysis

## 🔧 Configuration Management

### 1. Environment Configuration

#### Switching Environments
Update `appsettings.json`:
```json
{
  "BaseUrls": {
    "UAT": "https://uat.yourapp.com",
    "Staging": "https://staging.yourapp.com",
    "Production": "https://prod.yourapp.com"
  },
  "CurrentEnvironment": "UAT"
}
```

#### Environment-Specific Settings
```json
{
  "Environments": {
    "UAT": {
      "DatabaseConnection": "UAT_DB_STRING",
      "ApiEndpoint": "https://api-uat.yourapp.com"
    },
    "Production": {
      "DatabaseConnection": "PROD_DB_STRING",
      "ApiEndpoint": "https://api.yourapp.com"
    }
  }
}
```

### 2. Test Execution Settings

#### Timeouts and Waits
```json
{
  "GeneralWaitTimeForSpinnerInMilliseconds": 90000,
  "DelayBeforeStepExecustionInMilliseconds": 500,
  "PageLoadTimeoutInSeconds": 90
}
```

#### Browser Configuration
```json
{
  "BrowserSettings": {
    "DefaultBrowser": "Chrome",
    "Headless": false,
    "WindowSize": "1920x1080",
    "ImplicitWait": 10
  }
}
```

## 🛠️ Advanced Features

### 1. Custom Retry Logic

Use the `CustomRetryAttribute` for flaky tests:
```csharp
[Test]
[CustomRetry(3)] // Retry up to 3 times
public void FlakyNetworkTest()
{
    // Test implementation
}
```

### 2. Conditional Test Execution

Use conditions to control test execution:
```json
{
  "TestCase": "Conditional Test",
  "ExecutionConditions": {
    "Environment": "UAT",
    "BrowserType": "Chrome",
    "MinimumVersion": "1.0.0"
  }
}
```

### 3. Data-Driven Testing

Create parameterized tests:
```json
{
  "TestCase": "Login Test",
  "DataSets": [
    {
      "Name": "ValidUser1",
      "Data": {"username": "<EMAIL>", "password": "pass1"}
    },
    {
      "Name": "ValidUser2", 
      "Data": {"username": "<EMAIL>", "password": "pass2"}
    }
  ]
}
```

## 🐛 Debugging and Troubleshooting

### 1. Debug Mode

#### Enable Debug Logging
```json
{
  "LogLevel": "Debug"
}
```

#### Visual Studio Debugging
1. Set breakpoints in test code
2. Run tests in debug mode (F5)
3. Use immediate window for variable inspection

### 2. Common Issues

#### Test Discovery Problems
- Verify JSON syntax is valid
- Check file paths in configuration
- Ensure test files are in correct directories

#### WebDriver Issues
- Update browser to latest version
- Check ChromeDriver compatibility
- Verify network connectivity

#### Configuration Errors
- Validate JSON configuration files
- Check file permissions
- Verify environment variables

### 3. Logging and Diagnostics

#### Log Levels
- **Error**: Critical failures
- **Warn**: Potential issues
- **Info**: General information
- **Debug**: Detailed diagnostic info

#### Log Analysis
```bash
# Search for errors in logs
findstr "ERROR" Results\TextLogFiles\*.log

# Filter by test case
findstr "TC001" Results\TextLogFiles\*.log
```

## 📈 Best Practices

### 1. Test Design
- Keep tests independent and atomic
- Use descriptive test names
- Implement proper wait strategies
- Handle dynamic elements gracefully

### 2. Data Management
- Separate test data from test logic
- Use meaningful test data
- Implement data cleanup procedures
- Avoid hardcoded values

### 3. Maintenance
- Regular review and update of test cases
- Remove obsolete tests
- Update selectors for UI changes
- Monitor test execution performance

### 4. Reporting
- Review test results regularly
- Analyze failure patterns
- Update test documentation
- Share results with stakeholders

## 🔄 Continuous Integration

### 1. CI/CD Integration

#### Pipeline Configuration
```yaml
# Example Azure DevOps pipeline
steps:
- task: DotNetCoreCLI@2
  displayName: 'Run Tests'
  inputs:
    command: 'test'
    arguments: '--configuration Release --logger trx'
```

#### Scheduled Execution
```yaml
# Run tests daily at 2 AM
schedules:
- cron: "0 2 * * *"
  displayName: Daily test run
  branches:
    include:
    - main
```

### 2. Result Integration

#### Test Result Publishing
```yaml
- task: PublishTestResults@2
  inputs:
    testResultsFormat: 'VSTest'
    testResultsFiles: '**/*.trx'
```

#### Notification Setup
Configure notifications for:
- Test failures
- Performance degradation
- New test results available

## 📞 Support and Resources

### Getting Help
1. Check this user guide
2. Review log files for errors
3. Contact the development team
4. Create issues in the repository

### Additional Resources
- [Setup Guide](SETUP_GUIDE.md) - Installation instructions
- [Architecture Guide](ARCHITECTURE.md) - Technical details
- Sample test cases in the repository
- Framework API documentation
